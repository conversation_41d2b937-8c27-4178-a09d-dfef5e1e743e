<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToDo Management</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        header h1 {
            color: white;
            margin: 0;
        }
        .card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .card-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card-header h2 {
            margin: 0;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #2980b9;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background-color: #ddd;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
            cursor: pointer;
        }
        .tab.active {
            background-color: white;
            border-bottom: 2px solid #3498db;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }

        .placeholder-content {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
            font-style: italic;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .data-table th {
            background-color: #2c3e50;
            color: white;
            text-align: left;
            padding: 12px;
            border: 1px solid #ddd;
        }
        .data-table td {
            padding: 10px 12px;
            border: 1px solid #ddd;
            text-align: right;
        }
        .data-table td:first-child,
        .data-table td:nth-child(2) {
            text-align: left;
        }
        .data-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .data-table tr:hover {
            background-color: #e9ecef;
        }
        .action-btn {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 0;
        }
        .action-btn:hover {
            background-color: #c0392b;
        }
        .loading-message {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
            font-style: italic;
        }
        .refresh-btn {
            background-color: #27ae60;
            margin-bottom: 15px;
        }
        .refresh-btn:hover {
            background-color: #229954;
        }
    </style>
</head>
<body>
    <header>
        <h1>ToDo Management Dashboard</h1>
        <p>Manage tasks and items across different data types</p>
    </header>



    <div class="tabs">
        <div class="tab active" data-tab="plastic">Plastic</div>
        <div class="tab" data-tab="mold">Mold</div>
        <div class="tab" data-tab="mps">MPS</div>
        <div class="tab" data-tab="osl">OSL</div>
        <div class="tab" data-tab="disc">Disc</div>
        <div class="tab" data-tab="mps-review">MPS Review</div>
    </div>

    <div id="plastic" class="tab-content active">
        <div class="card">
            <div class="card-header">
                <h2>Plastic ToDo Items</h2>
            </div>
            <div class="placeholder-content">
                <h3>Plastic Management</h3>
                <p>This section will contain plastic-related todo items and management tools.</p>
                <p>Content coming soon...</p>
            </div>
        </div>
    </div>

    <div id="mold" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Mold ToDo Items</h2>
            </div>
            <div class="placeholder-content">
                <h3>Mold Management</h3>
                <p>This section will contain mold-related todo items and management tools.</p>
                <p>Content coming soon...</p>
            </div>
        </div>
    </div>

    <div id="mps" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>MPS ToDo Items</h2>
            </div>
            <div class="placeholder-content">
                <h3>MPS Management</h3>
                <p>This section will contain MPS-related todo items and management tools.</p>
                <p>Content coming soon...</p>
            </div>
        </div>
    </div>

    <div id="osl" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>OSL ToDo Items</h2>
            </div>
            <div class="placeholder-content">
                <h3>OSL Management</h3>
                <p>This section will contain OSL-related todo items and management tools.</p>
                <p>Content coming soon...</p>
            </div>
        </div>
    </div>

    <div id="disc" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Disc ToDo Items</h2>
            </div>
            <div class="placeholder-content">
                <h3>Disc Management</h3>
                <p>This section will contain disc-related todo items and management tools.</p>
                <p>Content coming soon...</p>
            </div>
        </div>
    </div>

    <div id="mps-review" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>MPS Review - Active MPS with No Stock</h2>
            </div>
            <div>
                <p>Review active MPS records that currently have no discs in stock. These may be candidates for marking as inactive.</p>
                <button id="refreshMpsReviewBtn" class="refresh-btn">Refresh Data</button>

                <div id="mpsReviewTableContainer">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>MPS ID</th>
                                <th>G Code</th>
                                <th>Last Sold Date</th>
                                <th>Last Received Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="mpsReviewTableBody">
                            <tr>
                                <td colspan="5" class="loading-message">Click "Refresh Data" to load MPS records...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab functionality
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs and content
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                // Add active class to clicked tab and corresponding content
                tab.classList.add('active');
                const tabId = tab.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // MPS Review functionality
        document.getElementById('refreshMpsReviewBtn').addEventListener('click', function() {
            loadMpsReviewData();
        });

        function loadMpsReviewData() {
            const tableBody = document.getElementById('mpsReviewTableBody');
            tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">Loading MPS review data...</td></tr>';

            // Call API to get MPS review data
            fetch('/api/mps-review')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.records) {
                        displayMpsReviewData(data.records);
                    } else {
                        tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">Error loading data: ' + (data.error || 'Unknown error') + '</td></tr>';
                    }
                })
                .catch(error => {
                    tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">Error loading data: ' + error.message + '. Make sure the adminServer.js is running.</td></tr>';
                });
        }

        function displayMpsReviewData(records) {
            const tableBody = document.getElementById('mpsReviewTableBody');

            if (!records || records.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">No active MPS records with zero stock found.</td></tr>';
                return;
            }

            tableBody.innerHTML = '';

            records.forEach(record => {
                const row = document.createElement('tr');

                // MPS ID
                const idCell = document.createElement('td');
                idCell.textContent = record.id;
                row.appendChild(idCell);

                // G Code
                const codeCell = document.createElement('td');
                codeCell.textContent = record.g_code || '';
                row.appendChild(codeCell);

                // Last Sold Date
                const soldDateCell = document.createElement('td');
                soldDateCell.textContent = record.sold_date_last ? new Date(record.sold_date_last).toLocaleDateString() : '';
                row.appendChild(soldDateCell);

                // Last Received Date
                const receivedDateCell = document.createElement('td');
                receivedDateCell.textContent = record.received_date_last ? new Date(record.received_date_last).toLocaleDateString() : '';
                row.appendChild(receivedDateCell);

                // Action button
                const actionCell = document.createElement('td');
                const actionBtn = document.createElement('button');
                actionBtn.textContent = 'Mark Inactive';
                actionBtn.className = 'action-btn';
                actionBtn.onclick = () => markMpsInactive(record.id);
                actionCell.appendChild(actionBtn);
                row.appendChild(actionCell);

                tableBody.appendChild(row);
            });
        }

        function markMpsInactive(mpsId) {
            if (!confirm(`Are you sure you want to mark MPS ID ${mpsId} as inactive?`)) {
                return;
            }

            // Call API to mark MPS as inactive
            fetch('/api/mps-mark-inactive', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ mpsId: mpsId }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('MPS marked as inactive successfully!');
                    // Refresh the data to remove the record from the list
                    loadMpsReviewData();
                } else {
                    alert('Error marking MPS as inactive: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                alert('Error marking MPS as inactive: ' + error.message);
            });
        }
    </script>
</body>
</html>
