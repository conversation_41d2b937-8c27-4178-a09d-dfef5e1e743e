// taskQueueWorker.js - A task queue worker that processes tasks from the t_task_queue table
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import { spawn } from 'child_process';
import processSdasinUpdatedFindDiscsToMatchTask from './processSdasinUpdatedFindDiscsToMatchTask.js';
import processNewTDiscsRecordTask from './processNewTDiscsRecordTask.js';
import processOslInsertedTask from './processOslInsertedTask.js';
import processOslUpdatedTask from './processOslUpdatedTask.js';
import processOslInsertedCreateInvOslTask from './processOslInsertedCreateInvOslTask.js';
import processSetInvOslTo0Task from './processSetInvOslTo0Task.js';
import processOslUpdatedUnlinkDiscsTask from './processOslUpdatedUnlinkDiscsTask.js';
import processMatchOslToDiscsTask from './processMatchOslToDiscsTask.js';
import processRenameAndUploadImagesTask from './processRenameAndUploadImagesTask.js';
import path from 'path';
import { fileURLToPath } from 'url';
import axios from 'axios';
import getVeeqoId from './getVeeqoId.js';

// Import Informed task handlers
import informedTaskHandler from './informedTaskHandler.cjs';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create a Supabase client using environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to log errors to the database
async function logError(message, context) {
  try {
    await supabase
      .from('t_error_logs')
      .insert({
        error_message: message,
        context: context,
        created_at: new Date().toISOString(),
        created_by: 'taskQueueWorker'
      });
  } catch (err) {
    console.error(`[taskQueueWorker.js] Exception while logging error: ${err.message}`);
  }
}

// Function to update task status
async function updateTaskStatus(taskId, status, result = null) {
  console.log(`[taskQueueWorker.js] Updating task ${taskId} status to ${status}`);

  try {
    const updateData = {
      status: status,
      processed_at: new Date().toISOString()
    };

    // Clear the lock if the task is completed, has an error, or is being set back to pending
    if (status === 'completed' || status === 'error' || status === 'pending') {
      updateData.locked_at = null;
      updateData.locked_by = null;
    }

    if (result) {
      // Pass the result object directly for JSONB column
      updateData.result = result;
    }

    const { error } = await supabase
      .from('t_task_queue')
      .update(updateData)
      .eq('id', taskId);

    if (error) {
      const errMsg = `[taskQueueWorker.js] Error updating task status: ${error.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating task ${taskId} status`);
      return false;
    }

    return true;
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while updating task status: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Updating task ${taskId} status`);
    return false;
  }
}

// Generate a unique worker ID for this instance
const workerId = `worker-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
console.log(`[taskQueueWorker.js] Worker ID: ${workerId}`);

// Function to fetch and lock pending tasks from the queue
async function fetchPendingTasks(limit = 100) { // Increased default limit to 100
  console.log('[taskQueueWorker.js] Fetching and locking pending tasks from t_task_queue...');

  try {
    // Get current timestamp for scheduled_at comparison
    const now = new Date().toISOString();
    console.log(`[taskQueueWorker.js] Current time: ${now}`);

    // Use the lock_pending_tasks function to atomically lock and return pending tasks
    const { data: tasks, error } = await supabase.rpc('lock_pending_tasks', {
      worker_id: `tqw_${workerId}`, // Add 'tqw_' prefix to the worker ID
      max_tasks: limit,
      task_time: now
    });

    if (error) {
      const errMsg = `[taskQueueWorker.js] Error fetching and locking pending tasks: ${error.message}`;
      console.error(errMsg);
      await logError(errMsg, 'Fetching and locking pending tasks');
      return [];
    }

    console.log(`[taskQueueWorker.js] Found and locked ${tasks ? tasks.length : 0} pending tasks ready to process`);

    if (!tasks || tasks.length === 0) {
      return [];
    }

    // Log the tasks for debugging
    tasks.forEach(task => {
      console.log(`[taskQueueWorker.js] Task ${task.id}: type=${task.task_type}, payload type=${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} payload: ${JSON.stringify(task.payload)}`);
    });

    return tasks;
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while fetching and locking pending tasks: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, 'Fetching and locking pending tasks');
    return [];
  }
}

// Function to process a verify_t_images_image task
async function processVerifyImageTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload to extract image_id - simplified to only handle the expected format
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format (no escaped quotes)
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', { error: 'Invalid JSON payload' });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Extract the image_id from the payload
    const imageId = payload.image_id || payload.id;

    if (!imageId) {
      const errMsg = `[taskQueueWorker.js] Missing image_id or id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', { error: 'Missing image_id or id in payload' });
      return;
    }

    console.log(`[taskQueueWorker.js] Verifying image with id=${imageId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Spawn the verifyImage.js process
    const verifyImagePath = path.join(__dirname, 'verifyImage.js');
    console.log(`[taskQueueWorker.js] Spawning process: node ${verifyImagePath} --id=${imageId}`);

    const verifyProcess = spawn('node', [verifyImagePath, `--id=${imageId}`]);

    // Collect stdout and stderr
    let stdout = '';
    let stderr = '';

    verifyProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    verifyProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    // Handle process completion
    verifyProcess.on('close', async (code) => {
      console.log(`[taskQueueWorker.js] verifyImage.js process exited with code ${code}`);

      if (code === 0) {
        // Success
        console.log(`[taskQueueWorker.js] Successfully verified image with id=${imageId}`);
        await updateTaskStatus(task.id, 'completed', {
          message: "Success! Image verified and t_images updated."
        });
      } else {
        // Failure
        console.log(`[taskQueueWorker.js] Failed to verify image with id=${imageId}`);
        console.log(`[taskQueueWorker.js] stdout: ${stdout}`);
        console.log(`[taskQueueWorker.js] stderr: ${stderr}`);

        await updateTaskStatus(task.id, 'completed', {
          message: "Image Failed. t_images updated and can be queued for verification later by editing the t_images table.",
          error: stderr || `Process exited with code ${code}`
        });
      }
    });

    // Handle process error
    verifyProcess.on('error', async (err) => {
      const errMsg = `[taskQueueWorker.js] Error spawning verifyImage.js process: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);

      await updateTaskStatus(task.id, 'error', {
        message: "Error spawning verifyImage.js process. The image was not verified.",
        error: err.message
      });
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Unexpected error during task processing. The image was not verified.",
      error: err.message
    });
  }
}

// Function to process an insert_new_t_images_record task
async function processInsertImageTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload - simplified to only handle the expected format
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format (no escaped quotes)
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to insert t_images record. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.table_name || !payload.record_id) {
      const errMsg = `[taskQueueWorker.js] Missing table_name or record_id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to insert t_images record. Missing required fields in payload.",
        error: 'Missing required fields in payload'
      });
      return;
    }

    const tableName = payload.table_name;
    const recordId = payload.record_id;

    console.log(`[taskQueueWorker.js] Inserting t_images record for ${tableName} with record_id=${recordId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // First, check if the record already exists
    const { data: existingRecord, error: checkError } = await supabase
      .from('t_images')
      .select('id')
      .eq('table_name', tableName)
      .eq('record_id', parseInt(recordId))
      .maybeSingle();

    if (checkError) {
      const errMsg = `[taskQueueWorker.js] Error checking for existing record: ${checkError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to insert t_images record. Database error when checking for existing record.",
        error: checkError.message,
        details: checkError
      });
      return;
    }

    if (existingRecord) {
      console.log(`[taskQueueWorker.js] Record already exists for ${tableName} with record_id=${recordId}`);
      await updateTaskStatus(task.id, 'completed', {
        message: `Success! t_images record already exists for ${tableName} with record_id=${recordId}.`
      });
      return;
    }

    // Insert the record
    console.log(`[taskQueueWorker.js] Inserting record with table_name=${tableName}, record_id=${recordId}`);

    // Simplify to only include essential fields
    const insertData = {
      table_name: tableName,
      record_id: parseInt(recordId)
      // Let the database handle created_by and created_at with defaults
    };

    console.log(`[taskQueueWorker.js] Insert data: ${JSON.stringify(insertData)}`);

    const { error: insertError } = await supabase
      .from('t_images')
      .insert(insertData);

    if (insertError) {
      const errMsg = `[taskQueueWorker.js] Error inserting t_images record: ${insertError.message}`;
      console.error(errMsg);
      console.error(`[taskQueueWorker.js] Full error object: ${JSON.stringify(insertError)}`);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to insert t_images record. Database error during insertion.",
        error: insertError.message,
        details: insertError
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Successfully inserted t_images record for ${tableName} with record_id=${recordId}`);
    await updateTaskStatus(task.id, 'completed', {
      message: `Success! New t_images record created for ${tableName} with record_id=${recordId}.`
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to insert t_images record due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a delete_t_images_record task
async function processDeleteImageTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload - simplified to only handle the expected format
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format (no escaped quotes)
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to delete t_images record. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.table_name || !payload.record_id) {
      const errMsg = `[taskQueueWorker.js] Missing table_name or record_id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to delete t_images record. Missing required fields in payload.",
        error: 'Missing required fields in payload'
      });
      return;
    }

    const tableName = payload.table_name;
    const recordId = payload.record_id;

    console.log(`[taskQueueWorker.js] Deleting t_images record for ${tableName} with record_id=${recordId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Try a direct delete approach
    console.log(`[taskQueueWorker.js] Attempting direct delete from t_images table`);

    // First, check if the record exists
    const { data: existingRecord, error: checkError } = await supabase
      .from('t_images')
      .select('id')
      .eq('table_name', tableName)
      .eq('record_id', parseInt(recordId))
      .maybeSingle();

    if (checkError) {
      console.error(`[taskQueueWorker.js] Error checking for existing record: ${checkError.message}`);
    } else if (!existingRecord) {
      console.log(`[taskQueueWorker.js] Record does not exist for ${tableName} with record_id=${recordId}`);
      // Record doesn't exist, so we can consider this a success
      console.log(`[taskQueueWorker.js] No record to delete for ${tableName} with record_id=${recordId}`);
      await updateTaskStatus(task.id, 'completed', {
        message: `Success! No t_images record exists for ${tableName} with record_id=${recordId}.`
      });
      return;
    }

    // Try the delete operation
    const { error: deleteError } = await supabase
      .from('t_images')
      .delete()
      .eq('table_name', tableName)
      .eq('record_id', parseInt(recordId));

    if (deleteError) {
      const errMsg = `[taskQueueWorker.js] Error deleting t_images record: ${deleteError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to delete t_images record. Database error during deletion.",
        error: deleteError.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Successfully deleted t_images record for ${tableName} with record_id=${recordId}`);
    await updateTaskStatus(task.id, 'completed', {
      message: `Success! t_images record deleted for ${tableName} with record_id=${recordId}.`
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to delete t_images record due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a check_if_disc_ready_to_publish task
async function processCheckDiscReadyTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if disc is ready to publish. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.disc_id) {
      const errMsg = `[taskQueueWorker.js] Missing disc_id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if disc is ready to publish. Missing disc_id in payload.",
        error: 'Missing disc_id in payload'
      });
      return;
    }

    const discId = payload.disc_id;

    console.log(`[taskQueueWorker.js] Checking if disc with id=${discId} is ready to publish`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Check if record is blocked by v_todo_discs
    const { data: todoDiscs, error: todoError } = await supabase
      .from('v_todo_discs')
      .select('id')
      .eq('id', discId)
      .maybeSingle();

    if (todoError) {
      const errMsg = `[taskQueueWorker.js] Error checking v_todo_discs: ${todoError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if disc is ready to publish. Database error.",
        error: todoError.message
      });
      return;
    }

    // If the disc is in v_todo_discs, it's not ready to publish
    if (todoDiscs) {
      console.log(`[taskQueueWorker.js] Disc ${discId} is not ready to publish - exists in v_todo_discs`);

      // Update t_discs with the note
      const { error: updateError } = await supabase
        .from('t_discs')
        .update({
          shopify_uploaded_notes: 'Disc Publish not queued - Waiting on v_todo_discs.'
        })
        .eq('id', discId);

      if (updateError) {
        const errMsg = `[taskQueueWorker.js] Error updating t_discs: ${updateError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Processing task ${task.id}`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to update t_discs with note. Database error.",
          error: updateError.message
        });
        return;
      }

      // Mark the task as completed with a failure result
      await updateTaskStatus(task.id, 'completed', {
        message: `Disc ${discId} is not ready to publish - exists in v_todo_discs`,
        success: false
      });

      return;
    }

    // If we get here, the disc is ready to publish
    console.log(`[taskQueueWorker.js] Disc ${discId} is ready to publish`);

    // Get the webhook prefix and compute the webhook URL
    const { data: webhookConfig, error: webhookError } = await supabase
      .from('t_config')
      .select('value')
      .eq('key', 'ngrok_endpoint')
      .maybeSingle();

    if (webhookError) {
      const errMsg = `[taskQueueWorker.js] Error getting webhook config: ${webhookError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to get webhook config. Database error.",
        error: webhookError.message
      });
      return;
    }

    const webhookPrefix = webhookConfig?.value || '';
    const webhookUrl = `https://${webhookPrefix}/publishProductDisc`;

    // Compute the safe scheduled time
    const { data: discData, error: discError } = await supabase
      .from('t_discs')
      .select('t_mps!inner(release_date_online)')
      .eq('id', discId)
      .maybeSingle();

    if (discError) {
      const errMsg = `[taskQueueWorker.js] Error getting disc data: ${discError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to get disc data. Database error.",
        error: discError.message
      });
      return;
    }

    // Calculate the scheduled time
    const releaseDate = discData?.t_mps?.release_date_online;
    const now = new Date();
    let scheduledTime;

    if (releaseDate) {
      // Use the release date minus 1 minute, but not earlier than now
      const releaseDateTime = new Date(releaseDate);
      releaseDateTime.setMinutes(releaseDateTime.getMinutes() - 1);
      scheduledTime = releaseDateTime > now ? releaseDateTime.toISOString() : now.toISOString();
    } else {
      // If no release date, use now
      scheduledTime = now.toISOString();
    }

    console.log(`[taskQueueWorker.js] Scheduled time for disc ${discId}: ${scheduledTime}`);

    // Create the publish task
    const { error: createTaskError } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'publish_product_disc',
        payload: { id: discId },
        status: 'pending',
        scheduled_at: scheduledTime,
        webhook: webhookUrl,
        created_at: new Date().toISOString(),
        enqueued_by: task.task_type
      });

    if (createTaskError) {
      const errMsg = `[taskQueueWorker.js] Error creating publish task: ${createTaskError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to create publish task. Database error.",
        error: createTaskError.message
      });
      return;
    }

    // Update t_discs with the note
    const { error: updateError } = await supabase
      .from('t_discs')
      .update({
        shopify_uploaded_notes: `Added to queue on ${new Date().toISOString()} to be published on ${scheduledTime}`
      })
      .eq('id', discId);

    if (updateError) {
      const errMsg = `[taskQueueWorker.js] Error updating t_discs: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update t_discs with note. Database error.",
        error: updateError.message
      });
      return;
    }

    // Log the task creation
    await logError('Task queued', `For product Disc ${discId} scheduled at ${scheduledTime}`, 'taskQueueWorker');

    // Mark the task as completed with a success result
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully queued publish task for disc ${discId} scheduled at ${scheduledTime}`,
      success: true
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to check if disc is ready to publish due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a verify_disc_image task
async function processVerifyDiscImageTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to verify disc image. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to verify disc image. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;

    console.log(`[taskQueueWorker.js] Verifying image for disc with id=${discId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // 1. Retrieve the disc record to get the image_file_name
    console.log(`[taskQueueWorker.js] Retrieving disc record for id=${discId}...`);
    const { data: discRecord, error: fetchError } = await supabase
      .from('t_discs')
      .select('image_file_name')
      .eq('id', discId)
      .maybeSingle();

    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error retrieving disc record: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Retrieving disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to verify disc image. Database error when retrieving disc record.",
        error: fetchError.message
      });
      return;
    }

    if (!discRecord) {
      const errMsg = `[taskQueueWorker.js] No disc record found for id=${discId}`;
      console.error(errMsg);
      await logError(errMsg, `No disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to verify disc image. Disc record not found.",
        error: 'Disc record not found'
      });
      return;
    }

    if (!discRecord.image_file_name) {
      const errMsg = `[taskQueueWorker.js] No image_file_name found for disc id=${discId}`;
      console.error(errMsg);
      await logError(errMsg, `Missing image_file_name for disc id=${discId}`);

      // Update the disc record with verification failure
      const { error: updateError } = await supabase
        .from('t_discs')
        .update({
          image_verified: false,
          image_verified_at: new Date().toISOString(),
          image_verified_by: 'taskQueueWorker',
          image_verified_notes: 'No image_file_name specified for this disc'
        })
        .eq('id', discId);

      if (updateError) {
        const errMsg = `[taskQueueWorker.js] Error updating disc record: ${updateError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Updating disc record for id=${discId}`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to update disc verification status. Database error.",
          error: updateError.message
        });
        return;
      }

      await updateTaskStatus(task.id, 'completed', {
        message: "Image verification failed. No image_file_name specified for this disc.",
        success: false
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Retrieved disc record with image_file_name=${discRecord.image_file_name}`);

    // 2. Retrieve configuration values from t_config
    console.log(`[taskQueueWorker.js] Retrieving configuration from t_config...`);
    const { data: configData, error: configError } = await supabase
      .from('t_config')
      .select('key, value')
      .in('key', ['public_image_server', 'folder_discs']);

    if (configError) {
      const errMsg = `[taskQueueWorker.js] Error retrieving t_config: ${configError.message}`;
      console.error(errMsg);
      await logError(errMsg, "Retrieving t_config values");
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to verify disc image. Database error when retrieving configuration.",
        error: configError.message
      });
      return;
    }

    const publicImageServer = configData.find(c => c.key === 'public_image_server')?.value;
    if (!publicImageServer) {
      const errMsg = '[taskQueueWorker.js] public_image_server not found in t_config';
      console.error(errMsg);
      await logError(errMsg, "Missing public_image_server in t_config");
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to verify disc image. Missing public_image_server configuration.",
        error: 'Missing public_image_server in t_config'
      });
      return;
    }

    const folderDiscs = configData.find(c => c.key === 'folder_discs')?.value;
    if (!folderDiscs) {
      const errMsg = '[taskQueueWorker.js] folder_discs not found in t_config';
      console.error(errMsg);
      await logError(errMsg, "Missing folder_discs in t_config");
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to verify disc image. Missing folder_discs configuration.",
        error: 'Missing folder_discs in t_config'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Using public_image_server=${publicImageServer} and folder_discs=${folderDiscs}`);

    // 3. Construct the image URL
    const fileName = discRecord.image_file_name;
    const subfolder = fileName.substring(0, 6);
    const constructedImageUrl = `${publicImageServer}/${folderDiscs}/${subfolder}/${fileName}.jpg`;

    console.log(`[taskQueueWorker.js] Constructed image URL: ${constructedImageUrl}`);

    // 4. Perform a HEAD request to check image accessibility
    console.log(`[taskQueueWorker.js] Performing HEAD request for URL: ${constructedImageUrl}`);

    try {
      const response = await fetch(constructedImageUrl, { method: 'HEAD' });
      console.log(`[taskQueueWorker.js] HEAD request completed. Status: ${response.status}`);
      const isOk = response.ok;
      console.log(`[taskQueueWorker.js] Image accessible: ${isOk}`);

      // 5. Update the disc record with the verification result
      const updatePayload = {
        image_verified: isOk,
        image_verified_at: new Date().toISOString(),
        image_verified_by: 'taskQueueWorker',
        image_verified_notes: isOk
          ? 'Success! Image is accessible'
          : `Not Found! Image not accessible, status code: ${response.status} for URL: ${constructedImageUrl}`
      };

      console.log(`[taskQueueWorker.js] Update payload for disc record id=${discId}: ${JSON.stringify(updatePayload)}`);

      const { error: updateError } = await supabase
        .from('t_discs')
        .update(updatePayload)
        .eq('id', discId);

      if (updateError) {
        const errMsg = `[taskQueueWorker.js] Error updating disc record: ${updateError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Updating disc record for id=${discId}`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to update disc verification status. Database error.",
          error: updateError.message
        });
        return;
      }

      console.log(`[taskQueueWorker.js] Successfully updated disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'completed', {
        message: isOk
          ? "Success! Disc image verified and t_discs updated."
          : "Image verification failed. t_discs updated with failure details.",
        success: isOk
      });
    } catch (fetchErr) {
      const errMsg = `[taskQueueWorker.js] Error performing HEAD request: ${fetchErr.message}`;
      console.error(errMsg);
      await logError(errMsg, `Performing HEAD request for URL: ${constructedImageUrl}`);

      // Still update the disc record with the error
      const updatePayload = {
        image_verified: false,
        image_verified_at: new Date().toISOString(),
        image_verified_by: 'taskQueueWorker',
        image_verified_notes: `Error! Failed to check image: ${fetchErr.message}`
      };

      const { error: updateError } = await supabase
        .from('t_discs')
        .update(updatePayload)
        .eq('id', discId);

      if (updateError) {
        const errMsg = `[taskQueueWorker.js] Error updating disc record: ${updateError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Updating disc record for id=${discId}`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to update disc verification status. Database error.",
          error: updateError.message
        });
        return;
      }

      await updateTaskStatus(task.id, 'completed', {
        message: "Image verification failed due to network error. t_discs updated with failure details.",
        success: false,
        error: fetchErr.message
      });
    }
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to verify disc image due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a clear_disc_verification task
async function processClearDiscVerificationTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to clear disc verification. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to clear disc verification. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;

    console.log(`[taskQueueWorker.js] Clearing verification for disc with id=${discId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Update the disc record to clear verification fields
    const updatePayload = {
      image_verified: null,
      image_verified_at: null,
      image_verified_by: null,
      image_verified_notes: `Verification cleared due to file name update - ${new Date().toISOString()}`
    };

    console.log(`[taskQueueWorker.js] Update payload for disc record id=${discId}: ${JSON.stringify(updatePayload)}`);

    const { error: updateError } = await supabase
      .from('t_discs')
      .update(updatePayload)
      .eq('id', discId);

    if (updateError) {
      const errMsg = `[taskQueueWorker.js] Error updating disc record: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to clear disc verification status. Database error.",
        error: updateError.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Successfully cleared verification for disc record id=${discId}`);
    await updateTaskStatus(task.id, 'completed', {
      message: "Success! Disc image verification cleared.",
      success: true
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to clear disc verification due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a check_if_disc_is_ready task
async function processCheckDiscIsReadyTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if disc is ready. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if disc is ready. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;

    console.log(`[taskQueueWorker.js] Checking if disc with id=${discId} is ready`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Retrieve the disc record to check all required fields
    console.log(`[taskQueueWorker.js] Retrieving disc record for id=${discId}...`);
    const { data: discRecord, error: fetchError } = await supabase
      .from('t_discs')
      .select('*, t_order_sheet_lines!left(shopify_uploaded_at, todo)')
      .eq('id', discId)
      .maybeSingle();

    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error retrieving disc record: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Retrieving disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if disc is ready. Database error when retrieving disc record.",
        error: fetchError.message
      });
      return;
    }

    if (!discRecord) {
      const errMsg = `[taskQueueWorker.js] No disc record found for id=${discId}`;
      console.error(errMsg);
      await logError(errMsg, `No disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if disc is ready. Disc record not found.",
        error: 'Disc record not found'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Retrieved disc record: ${JSON.stringify(discRecord)}`);

    // Check if all required fields are not null
    const requiredFields = [
      'image_file_name',
      'shipment_id',
      'weight',
      'color_id',
      'location',
      'order_sheet_line_id'
    ];

    const missingFields = [];
    for (const field of requiredFields) {
      if (discRecord[field] === null || discRecord[field] === undefined) {
        missingFields.push(field);
      }
    }

    // Check additional conditions
    const readyButtonIsTrue = discRecord.ready_button === true;
    const imageVerifiedIsTrue = discRecord.image_verified === true;
    const shopifyUploadedAtIsNull = discRecord.shopify_uploaded_at === null;

    // Check if order_sheet_line_id exists and if so, check if the related OSL has shopify_uploaded_at
    let oslShopifyUploadedAtIsNotNull = true; // Default to true if no order_sheet_line_id
    let hasOrderSheetLineId = discRecord.order_sheet_line_id !== null && discRecord.order_sheet_line_id !== undefined;

    if (hasOrderSheetLineId) {
      // If there is an order_sheet_line_id, check if the related OSL has shopify_uploaded_at
      const oslRecord = discRecord.t_order_sheet_lines;
      if (oslRecord) {
        oslShopifyUploadedAtIsNotNull = oslRecord.shopify_uploaded_at !== null;
        console.log(`[taskQueueWorker.js] OSL ${discRecord.order_sheet_line_id} shopify_uploaded_at: ${oslRecord.shopify_uploaded_at}`);
      } else {
        // If we couldn't find the OSL record, consider it not ready
        oslShopifyUploadedAtIsNotNull = false;
      }
    }

    // Determine if the disc is ready
    const isReady = missingFields.length === 0 && readyButtonIsTrue && imageVerifiedIsTrue && shopifyUploadedAtIsNull &&
                   (!hasOrderSheetLineId || oslShopifyUploadedAtIsNotNull);

    console.log(`[taskQueueWorker.js] Disc ${discId} readiness check:`);
    console.log(`[taskQueueWorker.js] - Missing fields: ${missingFields.length > 0 ? missingFields.join(', ') : 'None'}`);
    console.log(`[taskQueueWorker.js] - ready_button: ${readyButtonIsTrue}`);
    console.log(`[taskQueueWorker.js] - image_verified: ${imageVerifiedIsTrue}`);
    console.log(`[taskQueueWorker.js] - shopify_uploaded_at is null: ${shopifyUploadedAtIsNull}`);
    console.log(`[taskQueueWorker.js] - Has order_sheet_line_id: ${hasOrderSheetLineId}`);
    if (hasOrderSheetLineId) {
      console.log(`[taskQueueWorker.js] - OSL shopify_uploaded_at is not null: ${oslShopifyUploadedAtIsNotNull}`);
    }
    console.log(`[taskQueueWorker.js] - Overall ready status: ${isReady}`);

    // Prepare the update data
    const updateData = {
      ready_new: isReady
    };

    // If the disc is not ready, update the todo field with the reasons
    if (!isReady) {
      let reasons = [];
      if (missingFields.length > 0) {
        reasons.push(`Missing fields: ${missingFields.join(', ')}`);
      }
      if (!readyButtonIsTrue) {
        reasons.push('ready_button is not TRUE');
      }
      if (!imageVerifiedIsTrue) {
        reasons.push('image_verified is not TRUE');
      }
      if (!shopifyUploadedAtIsNull) {
        reasons.push('shopify_uploaded_at is not NULL');
      }

      // Special handling for OSL shopify_uploaded_at is NULL
      if (hasOrderSheetLineId && !oslShopifyUploadedAtIsNotNull) {
        // Get the OSL record and its todo field
        const oslRecord = discRecord.t_order_sheet_lines;
        let oslReason = 'OSL shopify_uploaded_at is NULL';

        // If the OSL has a todo field, include that information
        if (oslRecord && oslRecord.todo) {
          oslReason += ` (OSL todo: ${oslRecord.todo})`;
        }

        reasons.push(oslReason);
      }

      updateData.todo = `Disc is not ready. Reasons: ${reasons.join('; ')}`;
    }

    // Update the disc record
    const { error: updateError } = await supabase
      .from('t_discs')
      .update(updateData)
      .eq('id', discId);

    if (updateError) {
      const errMsg = `[taskQueueWorker.js] Error updating disc record: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update disc ready status. Database error.",
        error: updateError.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Successfully updated disc record for id=${discId} with ready_new=${isReady}`);

    // Prepare the result message
    let resultMessage;
    if (isReady) {
      resultMessage = "Success! Disc is ready. All required fields are present and conditions are met.";
    } else {
      // Use the same message that was set in the todo field
      resultMessage = updateData.todo;
    }

    // Mark the task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: resultMessage,
      success: true,
      ready: isReady,
      missing_fields: missingFields,
      ready_button: readyButtonIsTrue,
      image_verified: imageVerifiedIsTrue,
      shopify_uploaded_at_is_null: shopifyUploadedAtIsNull
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to check if disc is ready due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a publish_disc task
async function processPublishDiscTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to publish disc. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to publish disc. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;

    console.log(`[taskQueueWorker.js] Publishing disc with id=${discId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Spawn the publishProductDisc.js process
    const publishDiscPath = path.join(__dirname, 'publishProductDisc.js');
    console.log(`[taskQueueWorker.js] Spawning process: node ${publishDiscPath} --id=${discId}`);

    const publishProcess = spawn('node', [publishDiscPath, `--id=${discId}`]);

    // Collect stdout and stderr
    let stdout = '';
    let stderr = '';

    publishProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[publishProductDisc.js] ${output.trim()}`);
    });

    publishProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[publishProductDisc.js] ${output.trim()}`);
    });

    // Handle process completion
    publishProcess.on('close', async (code) => {
      console.log(`[taskQueueWorker.js] publishProductDisc.js process exited with code ${code}`);

      if (code === 0) {
        // Success
        console.log(`[taskQueueWorker.js] Successfully published disc with id=${discId}`);
        await updateTaskStatus(task.id, 'completed', {
          message: "Success! Disc published to Shopify.",
          stdout: stdout
        });
      } else {
        // Failure
        console.log(`[taskQueueWorker.js] Failed to publish disc with id=${discId}`);
        console.log(`[taskQueueWorker.js] stdout: ${stdout}`);
        console.log(`[taskQueueWorker.js] stderr: ${stderr}`);

        await updateTaskStatus(task.id, 'error', {
          message: "Failed to publish disc to Shopify.",
          error: stderr || `Process exited with code ${code}`,
          stdout: stdout
        });
      }
    });

    // Handle process error
    publishProcess.on('error', async (err) => {
      const errMsg = `[taskQueueWorker.js] Error spawning publishProductDisc.js process: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);

      await updateTaskStatus(task.id, 'error', {
        message: "Error spawning publishProductDisc.js process. The disc was not published.",
        error: err.message
      });
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to publish disc due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a generate_disc_title_pull_and_handle task
async function processGenerateDiscTitlePullAndHandleTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate disc title, pull, and handle. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate disc title, pull, and handle. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;

    console.log(`[taskQueueWorker.js] Generating title, pull, and handle for disc with id=${discId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Fetch the disc record with all necessary fields
    console.log(`[taskQueueWorker.js] Fetching disc record for id=${discId}...`);
    // Fetch the disc record with all the necessary fields
    let { data: discRecord, error: fetchError } = await supabase
      .from('t_discs')
      .select(`
        id,
        mps_id,
        weight,
        color_modifier,
        color_id,
        location,
        t_colors!left(id, color)
      `)
      .eq('id', discId)
      .maybeSingle();

    // If we have the disc record, follow the relationship chain to get all needed data
    if (!fetchError && discRecord && discRecord.mps_id) {
      console.log(`[taskQueueWorker.js] Fetching MPS record for mps_id=${discRecord.mps_id}...`);

      // Get the MPS record with all needed relationships
      const { data: mpsRecord, error: mpsError } = await supabase
        .from('t_mps')
        .select(`
          id,
          mold_id,
          plastic_id,
          stamp_id,
          t_molds!inner(id, mold, brand_id, speed, glide, turn, fade, t_brands!inner(id, brand)),
          t_plastics!inner(id, plastic),
          t_stamps!inner(id, stamp)
        `)
        .eq('id', discRecord.mps_id)
        .maybeSingle();

      if (mpsError) {
        const errMsg = `[taskQueueWorker.js] Error fetching MPS record: ${mpsError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Fetching MPS record for id=${discRecord.mps_id}`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to generate disc title, pull, and handle. Error fetching MPS record.",
          error: mpsError.message
        });
        return;
      }

      if (!mpsRecord) {
        const errMsg = `[taskQueueWorker.js] MPS record not found for mps_id=${discRecord.mps_id}`;
        console.error(errMsg);
        await logError(errMsg, `MPS record not found`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to generate disc title, pull, and handle. MPS record not found.",
          error: 'MPS record not found'
        });
        return;
      }

      // Check if we have all the required data
      if (!mpsRecord.t_molds) {
        const errMsg = `[taskQueueWorker.js] Mold data not found for mps_id=${discRecord.mps_id}`;
        console.error(errMsg);
        await logError(errMsg, `Mold data not found`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to generate disc title, pull, and handle. Mold data not found.",
          error: 'Mold data not found'
        });
        return;
      }

      if (!mpsRecord.t_plastics) {
        const errMsg = `[taskQueueWorker.js] Plastic data not found for mps_id=${discRecord.mps_id}`;
        console.error(errMsg);
        await logError(errMsg, `Plastic data not found`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to generate disc title, pull, and handle. Plastic data not found.",
          error: 'Plastic data not found'
        });
        return;
      }

      if (!mpsRecord.t_stamps) {
        const errMsg = `[taskQueueWorker.js] Stamp data not found for mps_id=${discRecord.mps_id}`;
        console.error(errMsg);
        await logError(errMsg, `Stamp data not found`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to generate disc title, pull, and handle. Stamp data not found.",
          error: 'Stamp data not found'
        });
        return;
      }

      if (!mpsRecord.t_molds.t_brands) {
        const errMsg = `[taskQueueWorker.js] Brand data not found for mold_id=${mpsRecord.mold_id}`;
        console.error(errMsg);
        await logError(errMsg, `Brand data not found`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to generate disc title, pull, and handle. Brand data not found.",
          error: 'Brand data not found'
        });
        return;
      }

      // Add all the required data to the disc record
      discRecord.brand = mpsRecord.t_molds.t_brands.brand;
      discRecord.plastic = mpsRecord.t_plastics.plastic;
      discRecord.mold = mpsRecord.t_molds.mold;
      discRecord.stamp = mpsRecord.t_stamps.stamp;

      // Add flight numbers from the mold
      discRecord.speed = mpsRecord.t_molds.speed;
      discRecord.glide = mpsRecord.t_molds.glide;
      discRecord.turn = mpsRecord.t_molds.turn;
      discRecord.fade = mpsRecord.t_molds.fade;

      // Get the color from the joined table
      if (discRecord.t_colors && discRecord.t_colors.color) {
        discRecord.color = discRecord.t_colors.color;
      } else if (discRecord.color_id) {
        // If the join didn't work but we have color_id, fetch it separately
        console.log(`[taskQueueWorker.js] Fetching color record for color_id=${discRecord.color_id}...`);
        const { data: colorRecord, error: colorError } = await supabase
          .from('t_colors')
          .select('id, color')
          .eq('id', discRecord.color_id)
          .maybeSingle();

        if (!colorError && colorRecord && colorRecord.color) {
          discRecord.color = colorRecord.color;
        } else if (colorError) {
          console.log(`[taskQueueWorker.js] Error fetching color: ${colorError.message}`);
        } else {
          console.log(`[taskQueueWorker.js] Color record not found or missing color name for color_id=${discRecord.color_id}`);
        }
      }

      // Log the retrieved data
      console.log(`[taskQueueWorker.js] Retrieved brand: ${discRecord.brand}`);
      console.log(`[taskQueueWorker.js] Retrieved plastic: ${discRecord.plastic}`);
      console.log(`[taskQueueWorker.js] Retrieved mold: ${discRecord.mold}`);
      console.log(`[taskQueueWorker.js] Retrieved stamp: ${discRecord.stamp}`);
      if (discRecord.color) {
        console.log(`[taskQueueWorker.js] Retrieved color: ${discRecord.color}`);
      }
      console.log(`[taskQueueWorker.js] Flight numbers: Speed=${discRecord.speed}, Glide=${discRecord.glide}, Turn=${discRecord.turn}, Fade=${discRecord.fade}`);
    } else if (!fetchError && (!discRecord || !discRecord.mps_id)) {
      const errMsg = `[taskQueueWorker.js] Disc record missing mps_id for disc_id=${discId}`;
      console.error(errMsg);
      await logError(errMsg, `Disc record missing mps_id`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate disc title, pull, and handle. Disc record missing mps_id.",
        error: 'Disc record missing mps_id'
      });
      return;
    }

    // Handle any error from the initial query
    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error fetching disc record: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate disc title, pull, and handle. Database error when retrieving disc record.",
        error: fetchError.message
      });
      return;
    }

    if (!discRecord) {
      const errMsg = `[taskQueueWorker.js] No disc record found for id=${discId}`;
      console.error(errMsg);
      await logError(errMsg, `No disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate disc title, pull, and handle. Disc record not found.",
        error: 'Disc record not found'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Retrieved disc record: ${JSON.stringify(discRecord)}`);

    // Calculate g_title
    let g_title = calculateDiscTitle(discRecord);
    console.log(`[taskQueueWorker.js] Calculated g_title: ${g_title}`);

    // Calculate g_pull
    let g_pull = calculateDiscPull(discRecord);
    console.log(`[taskQueueWorker.js] Calculated g_pull: ${g_pull}`);

    // Calculate g_handle
    let g_handle = calculateDiscHandle(discRecord, g_title);
    console.log(`[taskQueueWorker.js] Calculated g_handle: ${g_handle}`);

    // Update the disc record with the new values
    console.log(`[taskQueueWorker.js] Updating disc record with new values...`);
    const { error: updateError } = await supabase
      .from('t_discs')
      .update({
        g_title: g_title,
        g_pull: g_pull,
        g_handle: g_handle
      })
      .eq('id', discId);

    if (updateError) {
      const errMsg = `[taskQueueWorker.js] Error updating disc record: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update disc with new title, pull, and handle. Database error.",
        error: updateError.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Successfully updated disc record for id=${discId}`);
    await updateTaskStatus(task.id, 'completed', {
      message: "Success! Disc title, pull, and handle generated and updated.",
      g_title: g_title,
      g_pull: g_pull,
      g_handle: g_handle
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to generate disc title, pull, and handle due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to calculate disc title
function calculateDiscTitle(disc) {
  // Check for required fields
  if (!disc.brand || disc.brand.trim() === '') {
    return 'ERROR - THERE IS NO brand';
  }
  if (!disc.plastic || disc.plastic.trim() === '') {
    return 'ERROR - THERE IS NO plastic';
  }
  if (!disc.mold || disc.mold.trim() === '') {
    return 'ERROR - THERE IS NO mold';
  }
  if (!disc.weight || disc.weight.toString().trim() === '') {
    return 'ERROR - THERE IS NO weight';
  }
  if (!disc.stamp || disc.stamp.trim() === '') {
    return 'ERROR - THERE IS NO stamp';
  }

  // Build the title
  let title = `${disc.brand} ${disc.plastic} ${disc.mold}`;

  // Add stamp information if not 'Stock'
  if (disc.stamp !== 'Stock') {
    title += ` with ${disc.stamp}`;
    if (disc.color === 'Printed') {
      title += ' Print';
    } else {
      title += ' Stamp';
    }
  }

  // Add weight
  title += ` - ${disc.weight}g`;

  // Add color information if available
  if ((disc.color_modifier && disc.color_modifier.trim() !== '') ||
      (disc.color && disc.color.trim() !== '' && disc.color !== 'Printed')) {
    title += ' - ';

    // Add color modifier if available
    if (disc.color_modifier && disc.color_modifier.trim() !== '') {
      title += disc.color_modifier.trim();

      // Add space if both color modifier and color are present
      if (disc.color && disc.color.trim() !== '' && disc.color !== 'Printed') {
        title += ' ';
      }
    }

    // Add color if available and not 'Printed'
    if (disc.color && disc.color.trim() !== '' && disc.color !== 'Printed') {
      title += disc.color.trim();
    }
  }

  return title;
}

// Function to calculate disc pull
function calculateDiscPull(disc) {

  // Check location to determine pull format
  if (disc.location === 'FS') {
    // Format: F Brand Speed Mold Plastic Stamp ColorModifier Color WeightG #DiscID
    let pull = 'F';
    if (disc.brand) pull += ` ${disc.brand}`;
    if (disc.speed) pull += ` ${disc.speed}`;
    if (disc.mold) pull += ` ${disc.mold}`;
    if (disc.plastic) pull += ` ${disc.plastic}`;
    if (disc.stamp) pull += ` ${disc.stamp}`;
    if (disc.color_modifier && disc.color_modifier.trim()) pull += ` ${disc.color_modifier.trim()}`;
    if (disc.color) pull += ` ${disc.color}`;
    if (disc.weight) pull += ` ${disc.weight}g`;
    pull += ` #${disc.id}`;
    return pull;
  }
  else if (disc.location && disc.location.toLowerCase().startsWith('b2f')) {
    // Format: B2F #DiscID Brand Mold Plastic Stamp ColorModifier Color Weight
    let pull = 'B2F';
    pull += ` #${disc.id}`;
    if (disc.brand) pull += ` ${disc.brand}`;
    if (disc.mold) pull += ` ${disc.mold}`;
    if (disc.plastic) pull += ` ${disc.plastic}`;
    if (disc.stamp) pull += ` ${disc.stamp}`;
    if (disc.color_modifier && disc.color_modifier.trim()) pull += ` ${disc.color_modifier.trim()}`;
    if (disc.color) pull += ` ${disc.color}`;
    if (disc.weight) pull += ` ${disc.weight}`;
    return pull;
  }
  else if (disc.location === 'BS') {
    // Format: B #DiscID Brand Mold Plastic Stamp ColorModifier Color Weight
    let pull = 'B';
    pull += ` #${disc.id}`;
    if (disc.brand) pull += ` ${disc.brand}`;
    if (disc.mold) pull += ` ${disc.mold}`;
    if (disc.plastic) pull += ` ${disc.plastic}`;
    if (disc.stamp) pull += ` ${disc.stamp}`;
    if (disc.color_modifier && disc.color_modifier.trim()) pull += ` ${disc.color_modifier.trim()}`;
    if (disc.color) pull += ` ${disc.color}`;
    if (disc.weight) pull += ` ${disc.weight}`;
    return pull;
  }
  else {
    // Return null for other locations
    return null;
  }
}

// Function to calculate disc handle
function calculateDiscHandle(disc, title) {
  if (!title) {
    title = calculateDiscTitle(disc);
  }

  // Convert title to handle format
  let handle = title.toLowerCase();

  // Replace spaces with dashes
  handle = handle.replace(/ /g, '-');

  // Replace multiple dashes with a single dash
  handle = handle.replace(/-+/g, '-');

  // Add disc ID suffix
  handle += `-d${disc.id}`;

  return handle;
}

// Function to process a generate_disc_labels task
async function processGenerateDiscLabelsTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate disc labels. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Spawn the generateDiscLabels.js process
    const generateLabelsPath = path.join(__dirname, 'generateDiscLabels.js');

    // Prepare arguments based on payload
    const args = [];

    // If payload contains specific IDs, pass them to the script
    if (payload.ids && Array.isArray(payload.ids) && payload.ids.length > 0) {
      args.push(`--ids=${payload.ids.join(',')}`);
    }

    // If payload contains a limit, pass it to the script
    if (payload.limit && !isNaN(parseInt(payload.limit))) {
      args.push(`--limit=${payload.limit}`);
    }

    // If payload contains a specific query type, pass it to the script
    if (payload.queryType) {
      args.push(`--queryType=${payload.queryType}`);
    }

    console.log(`[taskQueueWorker.js] Spawning process: node ${generateLabelsPath} ${args.join(' ')}`);

    const generateProcess = spawn('node', [generateLabelsPath, ...args]);

    // Collect stdout and stderr
    let stdout = '';
    let stderr = '';

    generateProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[generateDiscLabels.js] ${output.trim()}`);
    });

    generateProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[generateDiscLabels.js] ${output.trim()}`);
    });

    // Handle process completion
    generateProcess.on('close', async (code) => {
      console.log(`[taskQueueWorker.js] generateDiscLabels.js process exited with code ${code}`);

      if (code === 0) {
        // Success
        console.log(`[taskQueueWorker.js] Successfully generated disc labels`);
        await updateTaskStatus(task.id, 'completed', {
          message: "Success! Disc labels generated.",
          stdout: stdout
        });
      } else {
        // Failure
        console.log(`[taskQueueWorker.js] Failed to generate disc labels`);
        console.log(`[taskQueueWorker.js] stdout: ${stdout}`);
        console.log(`[taskQueueWorker.js] stderr: ${stderr}`);

        await updateTaskStatus(task.id, 'completed', {
          message: "Failed to generate disc labels.",
          error: stderr || `Process exited with code ${code}`,
          stdout: stdout
        });
      }
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to generate disc labels due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a generate_mps_fields task
async function processGenerateMpsFieldsTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate MPS fields. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate MPS fields. Missing MPS id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const mpsId = payload.id;

    console.log(`[taskQueueWorker.js] Generating fields for MPS with id=${mpsId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Fetch the MPS record with all necessary relationships
    console.log(`[taskQueueWorker.js] Fetching MPS record for id=${mpsId}...`);
    const { data: mpsRecord, error: fetchError } = await supabase
      .from('t_mps')
      .select(`
        id,
        mold_id,
        plastic_id,
        stamp_id,
        description,
        override_speed,
        override_glide,
        override_turn,
        override_fade,
        t_molds!inner(id, mold, brand_id, speed, glide, turn, fade, type, description, t_brands!inner(id, brand)),
        t_plastics!inner(id, plastic),
        t_stamps!inner(id, stamp, description)
      `)
      .eq('id', mpsId)
      .maybeSingle();

    // Handle any error from the query
    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error fetching MPS record: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching MPS record for id=${mpsId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate MPS fields. Database error when retrieving MPS record.",
        error: fetchError.message
      });
      return;
    }

    if (!mpsRecord) {
      const errMsg = `[taskQueueWorker.js] MPS record not found for id=${mpsId}`;
      console.error(errMsg);
      await logError(errMsg, `MPS record not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate MPS fields. MPS record not found.",
        error: 'MPS record not found'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Retrieved MPS record: ${JSON.stringify(mpsRecord)}`);

    // Check if we have all the required data
    if (!mpsRecord.t_molds) {
      const errMsg = `[taskQueueWorker.js] Mold data not found for MPS id=${mpsId}`;
      console.error(errMsg);
      await logError(errMsg, `Mold data not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate MPS fields. Mold data not found.",
        error: 'Mold data not found'
      });
      return;
    }

    if (!mpsRecord.t_plastics) {
      const errMsg = `[taskQueueWorker.js] Plastic data not found for MPS id=${mpsId}`;
      console.error(errMsg);
      await logError(errMsg, `Plastic data not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate MPS fields. Plastic data not found.",
        error: 'Plastic data not found'
      });
      return;
    }

    if (!mpsRecord.t_stamps) {
      const errMsg = `[taskQueueWorker.js] Stamp data not found for MPS id=${mpsId}`;
      console.error(errMsg);
      await logError(errMsg, `Stamp data not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate MPS fields. Stamp data not found.",
        error: 'Stamp data not found'
      });
      return;
    }

    if (!mpsRecord.t_molds.t_brands) {
      const errMsg = `[taskQueueWorker.js] Brand data not found for mold_id=${mpsRecord.mold_id}`;
      console.error(errMsg);
      await logError(errMsg, `Brand data not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate MPS fields. Brand data not found.",
        error: 'Brand data not found'
      });
      return;
    }

    // Calculate g_handle
    const handle = calculateMpsHandle([
      mpsRecord.t_molds.t_brands.brand,
      mpsRecord.t_plastics.plastic,
      mpsRecord.t_molds.mold,
      mpsRecord.t_stamps.stamp
    ]);
    console.log(`[taskQueueWorker.js] Calculated g_handle: ${handle}`);

    // Calculate g_flight_numbers
    const flightNumbers = calculateMpsFlightNumbers(mpsRecord);
    console.log(`[taskQueueWorker.js] Calculated g_flight_numbers: ${flightNumbers}`);

    // Calculate g_code
    const code = `${mpsRecord.t_molds.mold} - ${mpsRecord.t_plastics.plastic} - ${mpsRecord.t_stamps.stamp}`;
    console.log(`[taskQueueWorker.js] Calculated g_code: ${code}`);

    // Calculate g_blurb_with_link
    const blurbWithLink = calculateMpsBlurbWithLink(mpsRecord, handle);
    console.log(`[taskQueueWorker.js] Calculated g_blurb_with_link: ${blurbWithLink}`);

    // Calculate g_seo_metafield_description
    const seoDescription = calculateMpsSeoDescription(mpsRecord);
    console.log(`[taskQueueWorker.js] Calculated g_seo_metafield_description: ${seoDescription}`);

    // Update the MPS record with the new values
    console.log(`[taskQueueWorker.js] Updating MPS record with new values...`);
    const { error: updateError } = await supabase
      .from('t_mps')
      .update({
        g_handle: handle,
        g_flight_numbers: flightNumbers,
        g_code: code,
        g_blurb_with_link: blurbWithLink,
        g_seo_metafield_description: seoDescription
      })
      .eq('id', mpsId);

    if (updateError) {
      const errMsg = `[taskQueueWorker.js] Error updating MPS record: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating MPS record for id=${mpsId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update MPS with new field values. Database error.",
        error: updateError.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Successfully updated MPS record for id=${mpsId}`);
    await updateTaskStatus(task.id, 'completed', {
      message: "Success! MPS fields generated and updated.",
      g_handle: handle,
      g_flight_numbers: flightNumbers,
      g_code: code,
      g_blurb_with_link: blurbWithLink.substring(0, 100) + '...' // Truncate for log readability
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to generate MPS fields due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a generate_osl_fields task
async function processGenerateOslFieldsTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate OSL fields. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate OSL fields. Missing OSL id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const oslId = payload.id;

    console.log(`[taskQueueWorker.js] Generating fields for OSL with id=${oslId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Fetch the OSL record with all necessary relationships
    console.log(`[taskQueueWorker.js] Fetching OSL record for id=${oslId}...`);
    const { data: oslRecord, error: fetchError } = await supabase
      .from('t_order_sheet_lines')
      .select(`
        id,
        min_weight,
        max_weight,
        color_id,
        mps_id,
        t_mps!inner(
          id,
          g_code
        ),
        t_colors!inner(id, color)
      `)
      .eq('id', oslId)
      .maybeSingle();

    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error fetching OSL record: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching OSL record for id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate OSL fields. Database error when retrieving OSL record.",
        error: fetchError.message
      });
      return;
    }

    if (!oslRecord) {
      const errMsg = `[taskQueueWorker.js] OSL record not found for id=${oslId}`;
      console.error(errMsg);
      await logError(errMsg, `OSL record not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate OSL fields. OSL record not found.",
        error: 'OSL record not found'
      });
      return;
    }

    // Check if we have all the necessary related data
    if (!oslRecord.t_mps) {
      const errMsg = `[taskQueueWorker.js] MPS data not found for mps_id=${oslRecord.mps_id}`;
      console.error(errMsg);
      await logError(errMsg, `MPS data not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate OSL fields. MPS data not found.",
        error: 'MPS data not found'
      });
      return;
    }

    if (!oslRecord.t_mps.g_code) {
      const errMsg = `[taskQueueWorker.js] MPS g_code not found for mps_id=${oslRecord.mps_id}`;
      console.error(errMsg);
      await logError(errMsg, `MPS g_code not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to generate OSL fields. MPS g_code not found.",
        error: 'MPS g_code not found'
      });
      return;
    }

    // Calculate g_code using t_mps.g_code as a base
    const code = `${oslRecord.t_mps.g_code} - ${oslRecord.min_weight} - ${oslRecord.max_weight} - ${oslRecord.t_colors.color}`;
    console.log(`[taskQueueWorker.js] Calculated g_code: ${code}`);

    // Update the OSL record with the new values
    console.log(`[taskQueueWorker.js] Updating OSL record with new values...`);
    const { error: updateError } = await supabase
      .from('t_order_sheet_lines')
      .update({
        g_code: code
      })
      .eq('id', oslId);

    if (updateError) {
      const errMsg = `[taskQueueWorker.js] Error updating OSL record: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating OSL record for id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update OSL with new field values. Database error.",
        error: updateError.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Successfully updated OSL record for id=${oslId}`);
    await updateTaskStatus(task.id, 'completed', {
      message: "Success! OSL fields generated and updated.",
      g_code: code
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to generate OSL fields due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a stamp_update_downstream_generate_mps_fields task
async function processStampUpdateDownstreamGenerateMpsFieldsTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process stamp update. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process stamp update. Missing stamp id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const stampId = payload.id;

    console.log(`[taskQueueWorker.js] Processing stamp update for stamp_id=${stampId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Fetch the stamp record to include in the result
    const { data: stampRecord, error: stampError } = await supabase
      .from('t_stamps')
      .select('id, stamp, description')
      .eq('id', stampId)
      .maybeSingle();

    if (stampError) {
      const errMsg = `[taskQueueWorker.js] Error fetching stamp record: ${stampError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching stamp record for id=${stampId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process stamp update. Database error when retrieving stamp record.",
        error: stampError.message
      });
      return;
    }

    if (!stampRecord) {
      const errMsg = `[taskQueueWorker.js] Stamp record not found for id=${stampId}`;
      console.error(errMsg);
      await logError(errMsg, `Stamp record not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process stamp update. Stamp record not found.",
        error: 'Stamp record not found'
      });
      return;
    }

    // Find all MPS records that use this stamp
    console.log(`[taskQueueWorker.js] Finding all MPS records with stamp_id=${stampId}...`);

    // Use pagination to handle potentially large number of records
    let allMpsRecords = [];
    let page = 0;
    const pageSize = 1000; // Supabase has a limit of 1000 records per query
    let hasMore = true;

    while (hasMore) {
      const { data: mpsRecords, error: mpsError, count } = await supabase
        .from('t_mps')
        .select('id', { count: 'exact' })
        .eq('stamp_id', stampId)
        .range(page * pageSize, (page + 1) * pageSize - 1);

      if (mpsError) {
        const errMsg = `[taskQueueWorker.js] Error fetching MPS records: ${mpsError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Fetching MPS records for stamp_id=${stampId}`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to process stamp update. Database error when retrieving MPS records.",
          error: mpsError.message
        });
        return;
      }

      if (mpsRecords && mpsRecords.length > 0) {
        allMpsRecords = [...allMpsRecords, ...mpsRecords];
      }

      // Check if we need to fetch more pages
      hasMore = mpsRecords && mpsRecords.length === pageSize;
      page++;
    }

    console.log(`[taskQueueWorker.js] Found ${allMpsRecords.length} MPS records with stamp_id=${stampId}`);

    // Enqueue generate_mps_fields tasks for each MPS record
    const enqueuedTasks = [];
    for (const mpsRecord of allMpsRecords) {
      try {
        const { data: taskData, error: taskError } = await supabase
          .from('t_task_queue')
          .insert([
            {
              task_type: 'generate_mps_fields',
              payload: { id: mpsRecord.id },
              status: 'pending',
              scheduled_at: new Date().toISOString(),
              created_at: new Date().toISOString(),
              enqueued_by: task.task_type
            }
          ])
          .select();

        if (taskError) {
          console.error(`[taskQueueWorker.js] Error enqueueing generate_mps_fields task for MPS id=${mpsRecord.id}: ${taskError.message}`);
          await logError(taskError.message, `Enqueueing generate_mps_fields task for MPS id=${mpsRecord.id}`);
        } else if (taskData && taskData.length > 0) {
          enqueuedTasks.push({ mps_id: mpsRecord.id, task_id: taskData[0].id });
        }
      } catch (err) {
        console.error(`[taskQueueWorker.js] Exception enqueueing generate_mps_fields task for MPS id=${mpsRecord.id}: ${err.message}`);
        await logError(err.message, `Enqueueing generate_mps_fields task for MPS id=${mpsRecord.id}`);
      }
    }

    console.log(`[taskQueueWorker.js] Successfully enqueued ${enqueuedTasks.length} generate_mps_fields tasks`);
    await updateTaskStatus(task.id, 'completed', {
      message: `Success! Enqueued ${enqueuedTasks.length} generate_mps_fields tasks for MPS records with stamp_id=${stampId}.`,
      stamp_id: stampId,
      stamp: stampRecord.stamp,
      enqueued_tasks_count: enqueuedTasks.length,
      enqueued_tasks: enqueuedTasks.slice(0, 100) // Limit to first 100 to avoid payload size issues
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process stamp update due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a mold_update_downstream_generate_mps_fields task
async function processMoldUpdateDownstreamGenerateMpsFieldsTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process mold update. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process mold update. Missing mold id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const moldId = payload.id;

    console.log(`[taskQueueWorker.js] Processing mold update for mold_id=${moldId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Fetch the mold record to include in the result
    const { data: moldRecord, error: moldError } = await supabase
      .from('t_molds')
      .select('id, mold, speed, glide, turn, fade, type, description, brand_id')
      .eq('id', moldId)
      .maybeSingle();

    if (moldError) {
      const errMsg = `[taskQueueWorker.js] Error fetching mold record: ${moldError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching mold record for id=${moldId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process mold update. Database error when retrieving mold record.",
        error: moldError.message
      });
      return;
    }

    if (!moldRecord) {
      const errMsg = `[taskQueueWorker.js] Mold record not found for id=${moldId}`;
      console.error(errMsg);
      await logError(errMsg, `Mold record not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process mold update. Mold record not found.",
        error: 'Mold record not found'
      });
      return;
    }

    // Find all MPS records that use this mold
    console.log(`[taskQueueWorker.js] Finding all MPS records with mold_id=${moldId}...`);

    // Use pagination to handle potentially large number of records
    let allMpsRecords = [];
    let page = 0;
    const pageSize = 1000; // Supabase has a limit of 1000 records per query
    let hasMore = true;

    while (hasMore) {
      const { data: mpsRecords, error: mpsError } = await supabase
        .from('t_mps')
        .select('id')
        .eq('mold_id', moldId)
        .range(page * pageSize, (page + 1) * pageSize - 1);

      if (mpsError) {
        const errMsg = `[taskQueueWorker.js] Error fetching MPS records: ${mpsError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Fetching MPS records for mold_id=${moldId}`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to process mold update. Database error when retrieving MPS records.",
          error: mpsError.message
        });
        return;
      }

      if (mpsRecords && mpsRecords.length > 0) {
        allMpsRecords = [...allMpsRecords, ...mpsRecords];
      }

      // Check if we need to fetch more pages
      hasMore = mpsRecords && mpsRecords.length === pageSize;
      page++;
    }

    console.log(`[taskQueueWorker.js] Found ${allMpsRecords.length} MPS records with mold_id=${moldId}`);

    // Enqueue generate_mps_fields tasks for each MPS record
    const enqueuedTasks = [];
    for (const mpsRecord of allMpsRecords) {
      try {
        const { data: taskData, error: taskError } = await supabase
          .from('t_task_queue')
          .insert([
            {
              task_type: 'generate_mps_fields',
              payload: { id: mpsRecord.id },
              status: 'pending',
              scheduled_at: new Date().toISOString(),
              created_at: new Date().toISOString(),
              enqueued_by: task.task_type
            }
          ])
          .select();

        if (taskError) {
          console.error(`[taskQueueWorker.js] Error enqueueing generate_mps_fields task for MPS id=${mpsRecord.id}: ${taskError.message}`);
          await logError(taskError.message, `Enqueueing generate_mps_fields task for MPS id=${mpsRecord.id}`);
        } else if (taskData && taskData.length > 0) {
          enqueuedTasks.push({ mps_id: mpsRecord.id, task_id: taskData[0].id });
        }
      } catch (err) {
        console.error(`[taskQueueWorker.js] Exception enqueueing generate_mps_fields task for MPS id=${mpsRecord.id}: ${err.message}`);
        await logError(err.message, `Enqueueing generate_mps_fields task for MPS id=${mpsRecord.id}`);
      }
    }

    console.log(`[taskQueueWorker.js] Successfully enqueued ${enqueuedTasks.length} generate_mps_fields tasks`);
    await updateTaskStatus(task.id, 'completed', {
      message: `Success! Enqueued ${enqueuedTasks.length} generate_mps_fields tasks for MPS records with mold_id=${moldId}.`,
      mold_id: moldId,
      mold: moldRecord.mold,
      enqueued_tasks_count: enqueuedTasks.length,
      enqueued_tasks: enqueuedTasks.slice(0, 100) // Limit to first 100 to avoid payload size issues
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process mold update due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a plastic_update_downstream_generate_mps_fields task
async function processPlasticUpdateDownstreamGenerateMpsFieldsTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process plastic update. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process plastic update. Missing plastic id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const plasticId = payload.id;

    console.log(`[taskQueueWorker.js] Processing plastic update for plastic_id=${plasticId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Fetch the plastic record to include in the result
    const { data: plasticRecord, error: plasticError } = await supabase
      .from('t_plastics')
      .select('id, plastic, description')
      .eq('id', plasticId)
      .maybeSingle();

    if (plasticError) {
      const errMsg = `[taskQueueWorker.js] Error fetching plastic record: ${plasticError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching plastic record for id=${plasticId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process plastic update. Database error when retrieving plastic record.",
        error: plasticError.message
      });
      return;
    }

    if (!plasticRecord) {
      const errMsg = `[taskQueueWorker.js] Plastic record not found for id=${plasticId}`;
      console.error(errMsg);
      await logError(errMsg, `Plastic record not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process plastic update. Plastic record not found.",
        error: 'Plastic record not found'
      });
      return;
    }

    // Find all MPS records that use this plastic
    console.log(`[taskQueueWorker.js] Finding all MPS records with plastic_id=${plasticId}...`);

    // Use pagination to handle potentially large number of records
    let allMpsRecords = [];
    let page = 0;
    const pageSize = 1000; // Supabase has a limit of 1000 records per query
    let hasMore = true;

    while (hasMore) {
      const { data: mpsRecords, error: mpsError } = await supabase
        .from('t_mps')
        .select('id')
        .eq('plastic_id', plasticId)
        .range(page * pageSize, (page + 1) * pageSize - 1);

      if (mpsError) {
        const errMsg = `[taskQueueWorker.js] Error fetching MPS records: ${mpsError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Fetching MPS records for plastic_id=${plasticId}`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to process plastic update. Database error when retrieving MPS records.",
          error: mpsError.message
        });
        return;
      }

      if (mpsRecords && mpsRecords.length > 0) {
        allMpsRecords = [...allMpsRecords, ...mpsRecords];
      }

      // Check if we need to fetch more pages
      hasMore = mpsRecords && mpsRecords.length === pageSize;
      page++;
    }

    console.log(`[taskQueueWorker.js] Found ${allMpsRecords.length} MPS records with plastic_id=${plasticId}`);

    // Enqueue generate_mps_fields tasks for each MPS record
    const enqueuedTasks = [];
    for (const mpsRecord of allMpsRecords) {
      try {
        const { data: taskData, error: taskError } = await supabase
          .from('t_task_queue')
          .insert([
            {
              task_type: 'generate_mps_fields',
              payload: { id: mpsRecord.id },
              status: 'pending',
              scheduled_at: new Date().toISOString(),
              created_at: new Date().toISOString(),
              enqueued_by: task.task_type
            }
          ])
          .select();

        if (taskError) {
          console.error(`[taskQueueWorker.js] Error enqueueing generate_mps_fields task for MPS id=${mpsRecord.id}: ${taskError.message}`);
          await logError(taskError.message, `Enqueueing generate_mps_fields task for MPS id=${mpsRecord.id}`);
        } else if (taskData && taskData.length > 0) {
          enqueuedTasks.push({ mps_id: mpsRecord.id, task_id: taskData[0].id });
        }
      } catch (err) {
        console.error(`[taskQueueWorker.js] Exception enqueueing generate_mps_fields task for MPS id=${mpsRecord.id}: ${err.message}`);
        await logError(err.message, `Enqueueing generate_mps_fields task for MPS id=${mpsRecord.id}`);
      }
    }

    console.log(`[taskQueueWorker.js] Successfully enqueued ${enqueuedTasks.length} generate_mps_fields tasks`);
    await updateTaskStatus(task.id, 'completed', {
      message: `Success! Enqueued ${enqueuedTasks.length} generate_mps_fields tasks for MPS records with plastic_id=${plasticId}.`,
      plastic_id: plasticId,
      plastic: plasticRecord.plastic,
      enqueued_tasks_count: enqueuedTasks.length,
      enqueued_tasks: enqueuedTasks.slice(0, 100) // Limit to first 100 to avoid payload size issues
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process plastic update due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process an update_veeqo_sdasin_qty task
async function processUpdateVeeqoSdasinQtyTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update Veeqo SDASIN quantity. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update Veeqo SDASIN quantity. Missing SDASIN id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    if (payload.available_quantity === undefined) {
      const errMsg = `[taskQueueWorker.js] Missing available_quantity in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update Veeqo SDASIN quantity. Missing available_quantity in payload.",
        error: 'Missing available_quantity in payload'
      });
      return;
    }

    const sdasinId = payload.id;
    const availableQuantity = payload.available_quantity;
    const sku = `Disc_${sdasinId}`;

    console.log(`[taskQueueWorker.js] Updating Veeqo quantity for SDASIN id=${sdasinId}, SKU=${sku}, quantity=${availableQuantity}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Get the Veeqo product IDs from the SKU
    const veeqoIds = await getVeeqoId(sku);

    if (!veeqoIds || veeqoIds.length === 0) {
      console.log(`[taskQueueWorker.js] No Veeqo product found for SKU ${sku}`);

      // Update the t_inv_sdasin record with the result
      const { error: updateError } = await supabase
        .from('t_inv_sdasin')
        .update({
          veeqo_qty_updated_at: new Date().toISOString(),
          veeqo_qty_update_notes: `SKU ${sku} not found in Veeqo`,
          veeqo_qty_update_full_http_response: null
        })
        .eq('id', sdasinId);

      if (updateError) {
        console.error(`[taskQueueWorker.js] Error updating t_inv_sdasin record: ${updateError.message}`);
        await logError(updateError.message, `Updating t_inv_sdasin record for id=${sdasinId}`);
      }

      await updateTaskStatus(task.id, 'completed', {
        message: `SKU ${sku} not found in Veeqo`,
        sdasin_id: sdasinId,
        sku: sku
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Found ${veeqoIds.length} Veeqo products for SKU ${sku}`);

    // Set the quantity for all Veeqo products with this SKU
    let successCount = 0;
    let failureCount = 0;
    let errors = [];
    let fullHttpResponse = '';

    for (const veeqoId of veeqoIds) {
      try {
        // Construct the API endpoint URL
        const veeqoApiKey = process.env.VEEQO_API_KEY;
        const url = `https://api.veeqo.com/sellables/${veeqoId}/warehouses/99881/stock_entry`;

        console.log(`[taskQueueWorker.js] Updating Veeqo quantity to ${availableQuantity} for product ID ${veeqoId}`);

        // Make the API request
        const response = await axios({
          method: 'PUT',
          url: url,
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': veeqoApiKey
          },
          data: {
            stock_entry: {
              physical_stock_level: availableQuantity,
              infinite: false
            }
          }
        });

        console.log(`[taskQueueWorker.js] Veeqo API response status: ${response.status}`);
        fullHttpResponse += `Product ID ${veeqoId}: Status ${response.status}\n`;

        if (response.data) {
          fullHttpResponse += `Response data: ${JSON.stringify(response.data)}\n`;
        }

        successCount++;
      } catch (err) {
        console.error(`[taskQueueWorker.js] Error updating Veeqo quantity for product ID ${veeqoId}: ${err.message}`);
        errors.push(`Product ID ${veeqoId}: ${err.message}`);
        fullHttpResponse += `Product ID ${veeqoId}: Error - ${err.message}\n`;
        failureCount++;
      }
    }

    // Update the t_inv_sdasin record with the result
    let veeqo_qty_update_notes = '';
    if (successCount > 0 && failureCount === 0) {
      veeqo_qty_update_notes = `Successfully updated ${successCount} Veeqo products`;
    } else if (successCount > 0 && failureCount > 0) {
      veeqo_qty_update_notes = `Partially updated Veeqo products: ${successCount} succeeded, ${failureCount} failed`;
    } else {
      veeqo_qty_update_notes = `Failed to update all ${failureCount} Veeqo products`;
    }

    const updateData = {
      veeqo_qty_updated_at: new Date().toISOString(),
      veeqo_qty_update_notes: veeqo_qty_update_notes,
      veeqo_qty_update_full_http_response: fullHttpResponse
    };

    const { error: updateError } = await supabase
      .from('t_inv_sdasin')
      .update(updateData)
      .eq('id', sdasinId);

    if (updateError) {
      console.error(`[taskQueueWorker.js] Error updating t_inv_sdasin record: ${updateError.message}`);
      await logError(updateError.message, `Updating t_inv_sdasin record for id=${sdasinId}`);
    }

    // Update the task status
    if (successCount > 0 && failureCount === 0) {
      await updateTaskStatus(task.id, 'completed', {
        message: `Successfully updated ${successCount} Veeqo products for SKU ${sku}`,
        sdasin_id: sdasinId,
        sku: sku,
        success_count: successCount
      });
    } else if (successCount > 0 && failureCount > 0) {
      await updateTaskStatus(task.id, 'completed', {
        message: `Partially updated Veeqo products for SKU ${sku}: ${successCount} succeeded, ${failureCount} failed`,
        sdasin_id: sdasinId,
        sku: sku,
        success_count: successCount,
        failure_count: failureCount,
        errors: errors
      });
    } else {
      await updateTaskStatus(task.id, 'error', {
        message: `Failed to update all Veeqo products for SKU ${sku}`,
        sdasin_id: sdasinId,
        sku: sku,
        failure_count: failureCount,
        errors: errors
      });
    }
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to update Veeqo SDASIN quantity due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process an update_veeqo_osl_qty task
async function processUpdateVeeqoOslQtyTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update Veeqo OSL quantity. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update Veeqo OSL quantity. Missing OSL id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    if (payload.available_quantity === undefined) {
      const errMsg = `[taskQueueWorker.js] Missing available_quantity in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update Veeqo OSL quantity. Missing available_quantity in payload.",
        error: 'Missing available_quantity in payload'
      });
      return;
    }

    const oslId = payload.id;
    const availableQuantity = payload.available_quantity;
    const sku = `OS${oslId}`;

    console.log(`[taskQueueWorker.js] Updating Veeqo quantity for OSL id=${oslId}, SKU=${sku}, quantity=${availableQuantity}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Get the Veeqo product IDs from the SKU
    const veeqoIds = await getVeeqoId(sku);

    if (!veeqoIds || veeqoIds.length === 0) {
      console.log(`[taskQueueWorker.js] No Veeqo product found for SKU ${sku}`);

      // Update the t_inv_osl record with the result
      const { error: updateError } = await supabase
        .from('t_inv_osl')
        .update({
          veeqo_qty_updated_at: new Date().toISOString(),
          veeqo_qty_update_notes: `SKU ${sku} not found in Veeqo`,
          veeqo_qty_update_full_http_response: null
        })
        .eq('id', oslId);

      if (updateError) {
        console.error(`[taskQueueWorker.js] Error updating t_inv_osl record: ${updateError.message}`);
        await logError(updateError.message, `Updating t_inv_osl record for id=${oslId}`);
      }

      await updateTaskStatus(task.id, 'completed', {
        message: `SKU ${sku} not found in Veeqo`,
        osl_id: oslId,
        sku: sku
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Found ${veeqoIds.length} Veeqo products for SKU ${sku}`);

    // Set the quantity for all Veeqo products with this SKU
    let successCount = 0;
    let failureCount = 0;
    let errors = [];
    let fullHttpResponse = '';

    for (const veeqoId of veeqoIds) {
      try {
        // Construct the API endpoint URL
        const veeqoApiKey = process.env.VEEQO_API_KEY;
        const url = `https://api.veeqo.com/sellables/${veeqoId}/warehouses/99881/stock_entry`;

        console.log(`[taskQueueWorker.js] Updating Veeqo quantity to ${availableQuantity} for product ID ${veeqoId}`);

        // Make the API request
        const response = await axios({
          method: 'PUT',
          url: url,
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': veeqoApiKey
          },
          data: {
            stock_entry: {
              physical_stock_level: availableQuantity,
              infinite: false
            }
          }
        });

        console.log(`[taskQueueWorker.js] Veeqo API response status: ${response.status}`);
        fullHttpResponse += `Product ID ${veeqoId}: Status ${response.status}\n`;

        if (response.data) {
          fullHttpResponse += `Response data: ${JSON.stringify(response.data)}\n`;
        }

        successCount++;
      } catch (err) {
        console.error(`[taskQueueWorker.js] Error updating Veeqo quantity for product ID ${veeqoId}: ${err.message}`);
        errors.push(`Product ID ${veeqoId}: ${err.message}`);
        fullHttpResponse += `Product ID ${veeqoId}: Error - ${err.message}\n`;
        failureCount++;
      }
    }

    // Update the t_inv_osl record with the result
    let veeqo_qty_update_notes = '';
    if (successCount > 0 && failureCount === 0) {
      veeqo_qty_update_notes = `Successfully updated ${successCount} Veeqo products`;
    } else if (successCount > 0 && failureCount > 0) {
      veeqo_qty_update_notes = `Partially updated Veeqo products: ${successCount} succeeded, ${failureCount} failed`;
    } else {
      veeqo_qty_update_notes = `Failed to update all ${failureCount} Veeqo products`;
    }

    const updateData = {
      veeqo_qty_updated_at: new Date().toISOString(),
      veeqo_qty_update_notes: veeqo_qty_update_notes,
      veeqo_qty_update_full_http_response: fullHttpResponse
    };

    const { error: updateError } = await supabase
      .from('t_inv_osl')
      .update(updateData)
      .eq('id', oslId);

    if (updateError) {
      console.error(`[taskQueueWorker.js] Error updating t_inv_osl record: ${updateError.message}`);
      await logError(updateError.message, `Updating t_inv_osl record for id=${oslId}`);
    }

    // Update the task status
    if (successCount > 0 && failureCount === 0) {
      await updateTaskStatus(task.id, 'completed', {
        message: `Successfully updated ${successCount} Veeqo products for SKU ${sku}`,
        osl_id: oslId,
        sku: sku,
        success_count: successCount
      });
    } else if (successCount > 0 && failureCount > 0) {
      await updateTaskStatus(task.id, 'completed', {
        message: `Partially updated Veeqo products for SKU ${sku}: ${successCount} succeeded, ${failureCount} failed`,
        osl_id: oslId,
        sku: sku,
        success_count: successCount,
        failure_count: failureCount,
        errors: errors
      });
    } else {
      await updateTaskStatus(task.id, 'error', {
        message: `Failed to update all Veeqo products for SKU ${sku}`,
        osl_id: oslId,
        sku: sku,
        failure_count: failureCount,
        errors: errors
      });
    }
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to update Veeqo OSL quantity due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a mps_g_code_update_downstream_generate_osl_fields task
async function processMpsGCodeUpdateDownstreamGenerateOslFieldsTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process MPS g_code update. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process MPS g_code update. Missing MPS id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const mpsId = payload.id;

    console.log(`[taskQueueWorker.js] Processing MPS g_code update for mps_id=${mpsId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Fetch the MPS record to include in the result
    const { data: mpsRecord, error: mpsError } = await supabase
      .from('t_mps')
      .select('id, g_code')
      .eq('id', mpsId)
      .maybeSingle();

    if (mpsError) {
      const errMsg = `[taskQueueWorker.js] Error fetching MPS record: ${mpsError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching MPS record for id=${mpsId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process MPS g_code update. Database error when retrieving MPS record.",
        error: mpsError.message
      });
      return;
    }

    if (!mpsRecord) {
      const errMsg = `[taskQueueWorker.js] MPS record not found for id=${mpsId}`;
      console.error(errMsg);
      await logError(errMsg, `MPS record not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process MPS g_code update. MPS record not found.",
        error: 'MPS record not found'
      });
      return;
    }

    // Find all OSL records that use this MPS
    console.log(`[taskQueueWorker.js] Finding all OSL records with mps_id=${mpsId}...`);

    // Use pagination to handle potentially large number of records
    let allOslRecords = [];
    let page = 0;
    const pageSize = 1000; // Supabase has a limit of 1000 records per query
    let hasMore = true;

    while (hasMore) {
      const { data: oslRecords, error: oslError } = await supabase
        .from('t_order_sheet_lines')
        .select('id')
        .eq('mps_id', mpsId)
        .range(page * pageSize, (page + 1) * pageSize - 1);

      if (oslError) {
        const errMsg = `[taskQueueWorker.js] Error fetching OSL records: ${oslError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Fetching OSL records for mps_id=${mpsId}`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to process MPS g_code update. Database error when retrieving OSL records.",
          error: oslError.message
        });
        return;
      }

      if (oslRecords && oslRecords.length > 0) {
        allOslRecords = [...allOslRecords, ...oslRecords];
      }

      // Check if we need to fetch more pages
      hasMore = oslRecords && oslRecords.length === pageSize;
      page++;
    }

    console.log(`[taskQueueWorker.js] Found ${allOslRecords.length} OSL records with mps_id=${mpsId}`);

    // Enqueue generate_osl_fields tasks for each OSL record
    const enqueuedTasks = [];
    for (const oslRecord of allOslRecords) {
      try {
        const { data: taskData, error: taskError } = await supabase
          .from('t_task_queue')
          .insert([
            {
              task_type: 'generate_osl_fields',
              payload: { id: oslRecord.id },
              status: 'pending',
              scheduled_at: new Date().toISOString(),
              created_at: new Date().toISOString(),
              enqueued_by: task.task_type
            }
          ])
          .select();

        if (taskError) {
          console.error(`[taskQueueWorker.js] Error enqueueing generate_osl_fields task for OSL id=${oslRecord.id}: ${taskError.message}`);
          await logError(taskError.message, `Enqueueing generate_osl_fields task for OSL id=${oslRecord.id}`);
        } else if (taskData && taskData.length > 0) {
          enqueuedTasks.push({ osl_id: oslRecord.id, task_id: taskData[0].id });
        }
      } catch (err) {
        console.error(`[taskQueueWorker.js] Exception enqueueing generate_osl_fields task for OSL id=${oslRecord.id}: ${err.message}`);
        await logError(err.message, `Enqueueing generate_osl_fields task for OSL id=${oslRecord.id}`);
      }
    }

    console.log(`[taskQueueWorker.js] Successfully enqueued ${enqueuedTasks.length} generate_osl_fields tasks`);
    await updateTaskStatus(task.id, 'completed', {
      message: `Success! Enqueued ${enqueuedTasks.length} generate_osl_fields tasks for OSL records with mps_id=${mpsId}.`,
      mps_id: mpsId,
      mps_g_code: mpsRecord.g_code,
      enqueued_tasks_count: enqueuedTasks.length,
      enqueued_tasks: enqueuedTasks.slice(0, 100) // Limit to first 100 to avoid payload size issues
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process MPS g_code update due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to calculate MPS handle
function calculateMpsHandle(parts) {
  // Filter out any null or empty parts
  const filteredParts = parts.filter(part => part && part.trim() !== '');

  // Join the parts with spaces and convert to lowercase
  let handle = filteredParts.join(' ').toLowerCase();

  // Replace spaces with dashes
  handle = handle.replace(/ /g, '-');

  // Replace any special characters with nothing
  handle = handle.replace(/[^a-z0-9-]/g, '');

  // Replace multiple dashes with a single dash
  handle = handle.replace(/-+/g, '-');

  // Remove leading and trailing dashes
  handle = handle.replace(/^-|-$/g, '');

  return handle;
}

// Function to calculate MPS flight numbers
function calculateMpsFlightNumbers(mps) {
  // Get the speed, glide, turn, and fade values, using overrides if available
  const speed = mps.override_speed !== null ? mps.override_speed : mps.t_molds.speed;
  const glide = mps.override_glide !== null ? mps.override_glide : mps.t_molds.glide;
  const turn = mps.override_turn !== null ? mps.override_turn : mps.t_molds.turn;
  const fade = mps.override_fade !== null ? mps.override_fade : mps.t_molds.fade;

  // Format each value to always have 1 decimal place
  const formattedSpeed = parseFloat(speed).toFixed(1);
  const formattedGlide = parseFloat(glide).toFixed(1);
  const formattedTurn = parseFloat(turn).toFixed(1);
  const formattedFade = parseFloat(fade).toFixed(1);

  // Format the flight numbers string
  return `Speed: ${formattedSpeed} | Glide: ${formattedGlide} | Turn: ${formattedTurn} | Fade: ${formattedFade}`;
}

// Function to calculate MPS blurb with link
function calculateMpsBlurbWithLink(mps, handle) {
  // Extract the necessary values
  const brand = mps.t_molds.t_brands.brand;
  const mold = mps.t_molds.mold;
  const type = mps.t_molds.type;
  const plastic = mps.t_plastics.plastic;
  const stamp = mps.t_stamps.stamp;
  const description = mps.description;
  const flightNumbers = calculateMpsFlightNumbers(mps);

  // Build the blurb with link
  let blurb = '';

  // First part: description or default text with link
  if (!description) {
    blurb = `<p>See All: <a href="/collections/${handle}">${plastic} ${mold}s with ${stamp} stamp.</a></p>`;
  } else {
    blurb = `<p>${description} <a href="/collections/${handle}">See All: ${plastic} ${mold}s with ${stamp} stamp.</a></p>`;
  }

  // Second part: mold information
  blurb += `<p><strong>Mold: The ${brand} ${mold} ${type} in ${plastic} Plastic  --  ${flightNumbers}</strong>  <br>`;

  return blurb;
}

// Function to calculate MPS SEO metafield description
function calculateMpsSeoDescription(mps) {
  // Use the formula provided by the user:
  // COALESCE(mps.description, case when s.stamp = 'Stock' then m.description else COALESCE(s.description, m.description) end)

  // Extract the necessary values
  const mpsDescription = mps.description;
  const moldDescription = mps.t_molds.description;
  const stampName = mps.t_stamps.stamp;
  const stampDescription = mps.t_stamps.description;

  // Apply the COALESCE logic
  let seoDescription;

  if (mpsDescription) {
    // If MPS description exists, use it
    seoDescription = mpsDescription;
  } else if (stampName === 'Stock') {
    // If stamp is 'Stock', use mold description
    seoDescription = moldDescription || ''; // Use empty string if moldDescription is null
  } else {
    // Otherwise use stamp description if it exists, otherwise mold description
    seoDescription = stampDescription || moldDescription || ''; // Use empty string if both are null
  }

  // If we don't have a description from any source, or it's just a period, create a structured one
  if (!seoDescription || seoDescription.trim() === '' || seoDescription.trim() === '.') {
    // Extract all the necessary values for the structured format
    const brand = mps.t_molds.t_brands.brand;
    const mold = mps.t_molds.mold;
    const type = mps.t_molds.type;
    const plastic = mps.t_plastics.plastic;
    const stamp = mps.t_stamps.stamp;
    const flightNumbers = calculateMpsFlightNumbers(mps);

    // Build the structured SEO description
    seoDescription = `${brand} ${mold} ${type} disc golf ${type.toLowerCase()} in ${plastic} plastic with ${stamp} stamp. ${flightNumbers}.`;
  }

  return seoDescription;
}

// Function to process a set_disc_carry_cost task
async function processSetDiscCarryCostTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to set disc carry cost. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to set disc carry cost. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;

    console.log(`[taskQueueWorker.js] Setting carry cost for disc with id=${discId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Log that the task is being processed
    await logError(
      `Processing set_disc_carry_cost task for disc_id=${discId}`,
      `Task ${task.id}`,
      'taskQueueWorker'
    );

    // Fetch the disc record with necessary relationships
    const { data: discRecord, error: fetchError } = await supabase
      .from('t_discs')
      .select(`
        id,
        mps_id,
        shipment_id
      `)
      .eq('id', discId)
      .maybeSingle();

    // Handle any error from the query
    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error fetching disc record: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to set disc carry cost. Database error when retrieving disc record.",
        error: fetchError.message
      });
      return;
    }

    if (!discRecord) {
      const errMsg = `[taskQueueWorker.js] Disc record not found for id=${discId}`;
      console.error(errMsg);
      await logError(errMsg, `Disc record not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to set disc carry cost. Disc record not found.",
        error: 'Disc record not found'
      });
      return;
    }

    // Check if we have all the required fields
    if (!discRecord.mps_id) {
      const errMsg = `[taskQueueWorker.js] Disc record missing mps_id for id=${discId}`;
      console.error(errMsg);
      await logError(errMsg, `Disc record missing mps_id`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to set disc carry cost. Disc record missing mps_id.",
        error: 'Disc record missing mps_id'
      });
      return;
    }

    if (!discRecord.shipment_id) {
      const errMsg = `[taskQueueWorker.js] Disc record missing shipment_id for id=${discId}`;
      console.error(errMsg);
      await logError(errMsg, `Disc record missing shipment_id`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to set disc carry cost. Disc record missing shipment_id.",
        error: 'Disc record missing shipment_id'
      });
      return;
    }

    // Fetch the order cost from t_mps or fallback to t_plastics
    const { data: orderCostData, error: orderCostError } = await supabase.rpc(
      'get_disc_order_cost',
      { mps_id_param: discRecord.mps_id }
    );

    if (orderCostError) {
      const errMsg = `[taskQueueWorker.js] Error fetching order cost: ${orderCostError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching order cost for mps_id=${discRecord.mps_id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to set disc carry cost. Error fetching order cost.",
        error: orderCostError.message
      });
      return;
    }

    if (!orderCostData || orderCostData.length === 0 || orderCostData[0].order_cost === null) {
      const errMsg = `[taskQueueWorker.js] Order cost is missing for disc with mps_id=${discRecord.mps_id}`;
      console.error(errMsg);
      await logError(errMsg, `Order cost missing`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to set disc carry cost. Order cost is missing.",
        error: 'Order cost missing'
      });
      return;
    }

    const orderCost = orderCostData[0].order_cost;
    console.log(`[taskQueueWorker.js] Retrieved order cost: ${orderCost}`);

    // Fetch the shipping multiplier from the invoice
    const { data: shippingMultiplierData, error: shippingMultiplierError } = await supabase.rpc(
      'get_shipment_multiplier',
      { shipment_id_param: discRecord.shipment_id }
    );

    if (shippingMultiplierError) {
      const errMsg = `[taskQueueWorker.js] Error fetching shipping multiplier: ${shippingMultiplierError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching shipping multiplier for shipment_id=${discRecord.shipment_id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to set disc carry cost. Error fetching shipping multiplier.",
        error: shippingMultiplierError.message
      });
      return;
    }

    if (!shippingMultiplierData || shippingMultiplierData.length === 0 || shippingMultiplierData[0].shipping_multiplier === null) {
      const errMsg = `[taskQueueWorker.js] Shipping multiplier is missing for disc with shipment_id=${discRecord.shipment_id}`;
      console.error(errMsg);
      await logError(errMsg, `Shipping multiplier missing`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to set disc carry cost. Shipping multiplier is missing.",
        error: 'Shipping multiplier missing'
      });
      return;
    }

    const shippingMultiplier = shippingMultiplierData[0].shipping_multiplier;
    console.log(`[taskQueueWorker.js] Retrieved shipping multiplier: ${shippingMultiplier}`);

    // Calculate the carrying cost
    const carryingCost = parseFloat((orderCost * shippingMultiplier).toFixed(4));
    console.log(`[taskQueueWorker.js] Calculated carrying cost: ${carryingCost}`);

    // Update the disc record with the carrying cost
    const { error: updateError } = await supabase
      .from('t_discs')
      .update({ carrying_cost: carryingCost })
      .eq('id', discId);

    if (updateError) {
      const errMsg = `[taskQueueWorker.js] Error updating disc record with carrying cost: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update disc with carrying cost. Database error.",
        error: updateError.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Successfully updated disc record with carrying cost for id=${discId}`);
    await updateTaskStatus(task.id, 'completed', {
      message: "Success! Disc carrying cost calculated and updated.",
      carrying_cost: carryingCost,
      order_cost: orderCost,
      shipping_multiplier: shippingMultiplier
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to set disc carry cost due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a match_disc_to_asins task
async function processMatchDiscToAsinsTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to match disc to SDASINs. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to match disc to SDASINs. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;

    console.log(`[taskQueueWorker.js] Matching disc with id=${discId} to SDASINs`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Log that the task is being processed
    await logError(
      `Processing match_disc_to_asins task for disc_id=${discId}`,
      `Task ${task.id}`,
      'taskQueueWorker'
    );

    // Fetch the disc record
    const { data: discRecord, error: fetchError } = await supabase
      .from('t_discs')
      .select(`
        id,
        mps_id,
        weight,
        color_id,
        sold_date
      `)
      .eq('id', discId)
      .maybeSingle();

    // Handle any error from the query
    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error fetching disc record: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to match disc to SDASINs. Database error when retrieving disc record.",
        error: fetchError.message
      });
      return;
    }

    if (!discRecord) {
      const errMsg = `[taskQueueWorker.js] Disc record not found for id=${discId}`;
      console.error(errMsg);
      await logError(errMsg, `Disc record not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to match disc to SDASINs. Disc record not found.",
        error: 'Disc record not found'
      });
      return;
    }

    // Check if we have all the required fields
    if (!discRecord.mps_id || !discRecord.weight || !discRecord.color_id) {
      const errMsg = `[taskQueueWorker.js] Disc record missing required fields for id=${discId}`;
      console.error(errMsg);
      await logError(errMsg, `Disc record missing required fields`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to match disc to SDASINs. Disc record missing required fields.",
        error: 'Disc record missing required fields'
      });
      return;
    }

    // Skip matching if disc was sold more than 14 days ago
    if (discRecord.sold_date) {
      const soldDate = new Date(discRecord.sold_date);
      const fourteenDaysAgo = new Date();
      fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 14);

      if (soldDate <= fourteenDaysAgo) {
        console.log(`[taskQueueWorker.js] Skipping matching for disc ${discId} as it was sold more than 14 days ago`);
        await updateTaskStatus(task.id, 'completed', {
          message: "Skipped matching as disc was sold more than 14 days ago."
        });

        // Update the disc record to mark that matching has been attempted
        await supabase
          .from('t_discs')
          .update({ looked_for_matching_sdasin_at: new Date().toISOString() })
          .eq('id', discId);

        return;
      }
    }

    // Delete old join records for this disc
    const { error: deleteError } = await supabase
      .from('tjoin_discs_sdasins')
      .delete()
      .eq('disc_id', discId);

    if (deleteError) {
      const errMsg = `[taskQueueWorker.js] Error deleting old join records: ${deleteError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Deleting old join records for disc_id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to match disc to SDASINs. Error deleting old join records.",
        error: deleteError.message
      });
      return;
    }

    // Use the optimized database function to match the disc to all SDASINs in a single operation
    console.log(`[taskQueueWorker.js] Matching disc ${discId} to all SDASINs using optimized function...`);

    const { data: matchResult, error: matchError } = await supabase.rpc(
      'match_disc_to_all_sdasins',
      {
        disc_id_param: discId
      }
    );

    if (matchError) {
      const errMsg = `[taskQueueWorker.js] Error matching disc to SDASINs: ${matchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Matching disc to SDASINs`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to match disc to SDASINs. Database error.",
        error: matchError.message
      });
      return;
    }

    const matchCount = matchResult;
    console.log(`[taskQueueWorker.js] Successfully matched disc ${discId} to ${matchCount} SDASINs using optimized function`);

    // The function already updates the disc record to mark that matching has been attempted
    await updateTaskStatus(task.id, 'completed', {
      message: `Success! Disc matched to ${matchCount} SDASINs.`,
      match_count: matchCount
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to match disc to SDASINs due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a match_disc_to_osl task
async function processMatchDiscToOslTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to match disc to OSL. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to match disc to OSL. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;
    const operation = payload.operation || 'UPDATE'; // Default to UPDATE if not specified
    const oldData = payload.old_data || null;

    console.log(`[taskQueueWorker.js] Matching disc with id=${discId} to OSL, operation=${operation}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Log that the task is being processed
    await logError(
      `Processing match_disc_to_osl task for disc_id=${discId}, operation=${operation}`,
      `Task ${task.id}`,
      'taskQueueWorker'
    );

    // Handle different operations
    if (operation === 'DELETE') {
      // Handle DELETE operation
      if (!oldData) {
        const errMsg = `[taskQueueWorker.js] Missing old_data for DELETE operation on disc_id=${discId}`;
        console.error(errMsg);
        await logError(errMsg, `Processing DELETE operation`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to match disc to OSL. Missing old data for DELETE operation.",
          error: 'Missing old_data'
        });
        return;
      }

      // On deletion, if disc unsold, decrease inventory
      if (oldData.order_sheet_line_id && !oldData.sold_date) {
        const { error: updateError } = await supabase.rpc(
          'update_osl_inventory',
          {
            osl_id_param: oldData.order_sheet_line_id,
            quantity_change_param: -1
          }
        );

        if (updateError) {
          const errMsg = `[taskQueueWorker.js] Error updating inventory for DELETE operation: ${updateError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Updating inventory for DELETE operation`);
          await updateTaskStatus(task.id, 'error', {
            message: "Failed to update inventory for DELETE operation.",
            error: updateError.message
          });
          return;
        }

        console.log(`[taskQueueWorker.js] Successfully decreased inventory for OSL ${oldData.order_sheet_line_id} on DELETE`);
      }

      await updateTaskStatus(task.id, 'completed', {
        message: "Success! Processed DELETE operation for disc.",
        operation: 'DELETE'
      });
      return;
    }

    // For INSERT and UPDATE operations, fetch the disc record
    const { data: discRecord, error: fetchError } = await supabase
      .from('t_discs')
      .select(`
        id,
        mps_id,
        weight,
        color_id,
        sold_date,
        order_sheet_line_id
      `)
      .eq('id', discId)
      .maybeSingle();

    // Handle any error from the query
    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error fetching disc record: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to match disc to OSL. Database error when retrieving disc record.",
        error: fetchError.message
      });
      return;
    }

    if (!discRecord) {
      const errMsg = `[taskQueueWorker.js] Disc record not found for id=${discId}`;
      console.error(errMsg);
      await logError(errMsg, `Disc record not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to match disc to OSL. Disc record not found.",
        error: 'Disc record not found'
      });
      return;
    }

    // Handle INSERT operation
    if (operation === 'INSERT') {
      // Find a matching order sheet line id based on criteria
      const { data: oslData, error: oslError } = await supabase.rpc(
        'find_matching_osl',
        {
          mps_id_param: discRecord.mps_id,
          color_id_param: discRecord.color_id,
          weight_param: discRecord.weight
        }
      );

      if (oslError) {
        const errMsg = `[taskQueueWorker.js] Error finding matching OSL: ${oslError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Finding matching OSL`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to find matching OSL. Database error.",
          error: oslError.message
        });
        return;
      }

      // Extract debug info and osl_id
      let debugInfo = 'No debug info available';
      let oslId = null;

      if (oslData && oslData.length > 0) {
        debugInfo = oslData[0].debug_info || debugInfo;
        oslId = oslData[0].osl_id;
      }

      console.log(`[taskQueueWorker.js] Debug info: ${debugInfo}`);

      if (!oslId) {
        const errMsg = `[taskQueueWorker.js] No matching order sheet line found for disc id ${discId}`;
        console.log(errMsg); // This is not an error, just a log
        await logError(errMsg, `No matching OSL found`, 'info');
        await updateTaskStatus(task.id, 'completed', {
          message: "No matching order sheet line found for disc.",
          operation: 'INSERT',
          debug_info: debugInfo
        });
        return;
      }

      // Check if the new OSL ID is different from the current one to prevent loops
      if (oslId === discRecord.order_sheet_line_id) {
        console.log(`[taskQueueWorker.js] New OSL ID ${oslId} is the same as current OSL ID. Skipping update to prevent loop.`);
      } else {
        // Update t_discs with the found order_sheet_line_id
        const { error: updateDiscError } = await supabase
          .from('t_discs')
          .update({ order_sheet_line_id: oslId })
          .eq('id', discId);

        if (updateDiscError) {
          const errMsg = `[taskQueueWorker.js] Error updating disc with OSL: ${updateDiscError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Updating disc with OSL`);
          await updateTaskStatus(task.id, 'error', {
            message: "Failed to update disc with OSL. Database error.",
            error: updateDiscError.message
          });
          return;
        }
      }

      // If the disc is unsold and we updated the OSL ID, increase the available quantity in t_inv_osl
      if (!discRecord.sold_date && oslId !== discRecord.order_sheet_line_id) {
        const { error: updateInvError } = await supabase.rpc(
          'update_osl_inventory',
          {
            osl_id_param: oslId,
            quantity_change_param: 1
          }
        );

        if (updateInvError) {
          const errMsg = `[taskQueueWorker.js] Error updating inventory: ${updateInvError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Updating inventory`);
          await updateTaskStatus(task.id, 'error', {
            message: "Failed to update inventory. Database error.",
            error: updateInvError.message
          });
          return;
        }
      }

      console.log(`[taskQueueWorker.js] Successfully matched disc ${discId} to OSL ${oslId}`);
      await updateTaskStatus(task.id, 'completed', {
        message: `Success! Disc matched to OSL ${oslId}.`,
        osl_id: oslId,
        operation: 'INSERT',
        debug_info: debugInfo
      });
      return;
    }

    // Handle UPDATE operation
    if (operation === 'UPDATE') {
      // Initialize variables for debug info and OSL ID
      let debugInfo = 'No debug info available';
      let newOslId = discRecord.order_sheet_line_id;

      if (!oldData) {
        const errMsg = `[taskQueueWorker.js] Missing old_data for UPDATE operation on disc_id=${discId}`;
        console.error(errMsg);
        await logError(errMsg, `Processing UPDATE operation`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to match disc to OSL. Missing old data for UPDATE operation.",
          error: 'Missing old_data'
        });
        return;
      }

      // Handle changes in sold_date
      if (!oldData.sold_date && discRecord.sold_date) {
        // Disc sold now; decrease inventory
        if (oldData.order_sheet_line_id) {
          const { error: updateInvError } = await supabase.rpc(
            'update_osl_inventory',
            {
              osl_id_param: oldData.order_sheet_line_id,
              quantity_change_param: -1
            }
          );

          if (updateInvError) {
            const errMsg = `[taskQueueWorker.js] Error updating inventory for sold disc: ${updateInvError.message}`;
            console.error(errMsg);
            await logError(errMsg, `Updating inventory for sold disc`);
            await updateTaskStatus(task.id, 'error', {
              message: "Failed to update inventory for sold disc. Database error.",
              error: updateInvError.message
            });
            return;
          }

          console.log(`[taskQueueWorker.js] Successfully decreased inventory for OSL ${oldData.order_sheet_line_id} on disc sold`);
        }
      } else if (oldData.sold_date && !discRecord.sold_date) {
        // Disc marked unsold; increase inventory
        if (discRecord.order_sheet_line_id) {
          const { error: updateInvError } = await supabase.rpc(
            'update_osl_inventory',
            {
              osl_id_param: discRecord.order_sheet_line_id,
              quantity_change_param: 1
            }
          );

          if (updateInvError) {
            const errMsg = `[taskQueueWorker.js] Error updating inventory for unsold disc: ${updateInvError.message}`;
            console.error(errMsg);
            await logError(errMsg, `Updating inventory for unsold disc`);
            await updateTaskStatus(task.id, 'error', {
              message: "Failed to update inventory for unsold disc. Database error.",
              error: updateInvError.message
            });
            return;
          }

          console.log(`[taskQueueWorker.js] Successfully increased inventory for OSL ${discRecord.order_sheet_line_id} on disc unsold`);
        }
      }

      // Handle changes to weight, color, or mps_id, or if the disc doesn't have an order_sheet_line_id
      if (oldData.weight !== discRecord.weight ||
          oldData.color_id !== discRecord.color_id ||
          oldData.mps_id !== discRecord.mps_id ||
          !discRecord.order_sheet_line_id) {

        // Decrease inventory on old order_sheet_line_id
        if (oldData.order_sheet_line_id && !oldData.sold_date) {
          const { error: updateInvError } = await supabase.rpc(
            'update_osl_inventory',
            {
              osl_id_param: oldData.order_sheet_line_id,
              quantity_change_param: -1
            }
          );

          if (updateInvError) {
            const errMsg = `[taskQueueWorker.js] Error updating inventory for old OSL: ${updateInvError.message}`;
            console.error(errMsg);
            await logError(errMsg, `Updating inventory for old OSL`);
            await updateTaskStatus(task.id, 'error', {
              message: "Failed to update inventory for old OSL. Database error.",
              error: updateInvError.message
            });
            return;
          }

          console.log(`[taskQueueWorker.js] Successfully decreased inventory for old OSL ${oldData.order_sheet_line_id}`);
        }

        // Find new matching order sheet line
        const { data: oslData, error: oslError } = await supabase.rpc(
          'find_matching_osl',
          {
            mps_id_param: discRecord.mps_id,
            color_id_param: discRecord.color_id,
            weight_param: discRecord.weight
          }
        );

        if (oslError) {
          const errMsg = `[taskQueueWorker.js] Error finding new matching OSL: ${oslError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Finding new matching OSL`);
          await updateTaskStatus(task.id, 'error', {
            message: "Failed to find new matching OSL. Database error.",
            error: oslError.message
          });
          return;
        }

        // Extract debug info and osl_id
        let debugInfo = 'No debug info available';
        let newOslId = null;

        if (oslData && oslData.length > 0) {
          debugInfo = oslData[0].debug_info || debugInfo;
          newOslId = oslData[0].osl_id;
        }

        console.log(`[taskQueueWorker.js] Debug info: ${debugInfo}`);

        // Check if the new OSL ID is different from the current one to prevent loops
        if (newOslId === discRecord.order_sheet_line_id) {
          console.log(`[taskQueueWorker.js] New OSL ID ${newOslId} is the same as current OSL ID. Skipping update to prevent loop.`);
        } else {
          // Update the disc with the new order_sheet_line_id
          const { error: updateDiscError } = await supabase
            .from('t_discs')
            .update({ order_sheet_line_id: newOslId })
            .eq('id', discId);

          if (updateDiscError) {
            const errMsg = `[taskQueueWorker.js] Error updating disc with new OSL: ${updateDiscError.message}`;
            console.error(errMsg);
            await logError(errMsg, `Updating disc with new OSL`);
            await updateTaskStatus(task.id, 'error', {
              message: "Failed to update disc with new OSL. Database error.",
              error: updateDiscError.message,
              debug_info: debugInfo
            });
            return;
          }
        }

        // Increase inventory in new order sheet line if disc is unsold and we actually updated the OSL ID
        if (newOslId && !discRecord.sold_date && newOslId !== discRecord.order_sheet_line_id) {
          const { error: updateInvError } = await supabase.rpc(
            'update_osl_inventory',
            {
              osl_id_param: newOslId,
              quantity_change_param: 1
            }
          );

          if (updateInvError) {
            const errMsg = `[taskQueueWorker.js] Error updating inventory for new OSL: ${updateInvError.message}`;
            console.error(errMsg);
            await logError(errMsg, `Updating inventory for new OSL`);
            await updateTaskStatus(task.id, 'error', {
              message: "Failed to update inventory for new OSL. Database error.",
              error: updateInvError.message
            });
            return;
          }

          console.log(`[taskQueueWorker.js] Successfully increased inventory for new OSL ${newOslId}`);
        }
      }

      console.log(`[taskQueueWorker.js] Successfully processed UPDATE operation for disc ${discId}`);
      await updateTaskStatus(task.id, 'completed', {
        message: newOslId ?
          `Success! Disc matched to OSL ${newOslId}.` :
          "No matching order sheet line found for disc.",
        operation: 'UPDATE',
        debug_info: debugInfo,
        osl_id: newOslId
      });
      return;
    }

    // If we get here, the operation is not supported
    const errMsg = `[taskQueueWorker.js] Unsupported operation: ${operation}`;
    console.error(errMsg);
    await logError(errMsg, `Unsupported operation`);
    await updateTaskStatus(task.id, 'error', {
      message: "Failed to match disc to OSL. Unsupported operation.",
      error: `Unsupported operation: ${operation}`
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to match disc to OSL due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a publish_product_disc_verify task
async function processPublishProductDiscVerifyTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to verify disc publishing. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to verify disc publishing. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;

    console.log(`[taskQueueWorker.js] Verifying publishing status for disc with id=${discId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Check if the disc has been published
    const { data: discRecord, error: fetchError } = await supabase
      .from('t_discs')
      .select('id, shopify_uploaded_at')
      .eq('id', discId)
      .maybeSingle();

    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error fetching disc record: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to verify disc publishing. Database error when retrieving disc record.",
        error: fetchError.message
      });
      return;
    }

    if (!discRecord) {
      const errMsg = `[taskQueueWorker.js] Disc record not found for id=${discId}`;
      console.error(errMsg);
      await logError(errMsg, `Disc record not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to verify disc publishing. Disc record not found.",
        error: 'Disc record not found'
      });
      return;
    }

    // Check if the disc has been published
    if (discRecord.shopify_uploaded_at) {
      console.log(`[taskQueueWorker.js] Disc ${discId} has been published at ${discRecord.shopify_uploaded_at}`);
      await updateTaskStatus(task.id, 'completed', {
        message: "Success! Disc has been published to Shopify.",
        shopify_uploaded_at: discRecord.shopify_uploaded_at
      });
      return;
    }

    // If we get here, the disc has not been published yet
    console.log(`[taskQueueWorker.js] Disc ${discId} has not been published yet`);

    // Check if we've exceeded the maximum number of retries
    const { data: taskRecord, error: taskError } = await supabase
      .from('t_task_queue')
      .select('fails')
      .eq('id', task.id)
      .maybeSingle();

    if (taskError) {
      const errMsg = `[taskQueueWorker.js] Error fetching task record: ${taskError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching task record for id=${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to verify disc publishing. Database error when retrieving task record.",
        error: taskError.message
      });
      return;
    }

    const fails = taskRecord?.fails || 0;
    if (fails >= 5) {
      console.log(`[taskQueueWorker.js] Exceeded maximum number of retries (${fails}) for task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to verify disc publishing. Exceeded maximum number of retries.",
        fails: fails
      });
      return;
    }

    // Reschedule the task for 5 minutes later
    const scheduledAt = new Date();
    scheduledAt.setMinutes(scheduledAt.getMinutes() + 5);

    const { error: updateError } = await supabase
      .from('t_task_queue')
      .update({
        status: 'pending',
        fails: fails + 1,
        scheduled_at: scheduledAt.toISOString()
      })
      .eq('id', task.id);

    if (updateError) {
      const errMsg = `[taskQueueWorker.js] Error rescheduling task: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Rescheduling task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to reschedule verification task. Database error.",
        error: updateError.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Rescheduled task ${task.id} for ${scheduledAt.toISOString()}`);
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to verify disc publishing due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a publish_product_osl task
async function processPublishProductOslTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to publish OSL. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to publish OSL. Missing OSL id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const oslId = payload.id;

    console.log(`[taskQueueWorker.js] Publishing OSL with id=${oslId}`);

    // Check if the OSL has already been published
    const { data: oslRecord, error: fetchError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, shopify_uploaded_at')
      .eq('id', oslId)
      .maybeSingle();

    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error fetching OSL record: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching OSL record for id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to publish OSL. Database error when retrieving OSL record.",
        error: fetchError.message
      });
      return;
    }

    if (!oslRecord) {
      const errMsg = `[taskQueueWorker.js] OSL record not found for id=${oslId}`;
      console.error(errMsg);
      await logError(errMsg, `OSL record not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to publish OSL. OSL record not found.",
        error: 'OSL record not found'
      });
      return;
    }

    // If the OSL has already been published, mark the task as completed
    if (oslRecord.shopify_uploaded_at) {
      console.log(`[taskQueueWorker.js] OSL ${oslId} has already been published at ${oslRecord.shopify_uploaded_at}`);
      await updateTaskStatus(task.id, 'completed', {
        message: "OSL has already been published to Shopify.",
        shopify_uploaded_at: oslRecord.shopify_uploaded_at
      });
      return;
    }

    // This task is ready to be processed normally
    console.log(`[taskQueueWorker.js] Task ${task.id} is ready for normal processing`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Spawn the publishProductOSL.js process
    const publishOslPath = path.join(__dirname, 'publishProductOSL.js');
    console.log(`[taskQueueWorker.js] Spawning process: node ${publishOslPath} --id=${oslId}`);

    const publishProcess = spawn('node', [publishOslPath, `--id=${oslId}`]);

    // Collect stdout and stderr
    let stdout = '';
    let stderr = '';

    publishProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[publishProductOSL.js] ${output.trim()}`);
    });

    publishProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[publishProductOSL.js] ${output.trim()}`);
    });

    // Handle process completion
    publishProcess.on('close', async (code) => {
      console.log(`[taskQueueWorker.js] publishProductOSL.js process exited with code ${code}`);

      if (code === 0) {
        // Success
        console.log(`[taskQueueWorker.js] Successfully published OSL with id=${oslId}`);
        await updateTaskStatus(task.id, 'completed', {
          message: "Success! OSL published to Shopify.",
          stdout: stdout
        });

        // Create a verification task
        const { error: createTaskError } = await supabase
          .from('t_task_queue')
          .insert({
            task_type: 'publish_product_osl_verify',
            payload: payload,
            status: 'pending',
            scheduled_at: new Date(Date.now() + 60000).toISOString(), // Schedule for 1 minute later
            created_at: new Date().toISOString(),
            enqueued_by: task.task_type
          });

        if (createTaskError) {
          const errMsg = `[taskQueueWorker.js] Error creating verification task: ${createTaskError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Creating verification task`);
        } else {
          console.log(`[taskQueueWorker.js] Created verification task for OSL ${oslId}`);
        }
      } else {
        // Failure
        console.log(`[taskQueueWorker.js] Failed to publish OSL with id=${oslId}`);
        console.log(`[taskQueueWorker.js] stdout: ${stdout}`);
        console.log(`[taskQueueWorker.js] stderr: ${stderr}`);

        await updateTaskStatus(task.id, 'error', {
          message: "Failed to publish OSL to Shopify.",
          error: stderr || `Process exited with code ${code}`,
          stdout: stdout
        });
      }
    });

    // Handle process error
    publishProcess.on('error', async (err) => {
      const errMsg = `[taskQueueWorker.js] Error spawning publishProductOSL.js process: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);

      await updateTaskStatus(task.id, 'error', {
        message: "Error spawning publishProductOSL.js process. The OSL was not published.",
        error: err.message
      });
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to publish OSL due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a publish_product_osl_verify task
async function processPublishProductOslVerifyTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to verify OSL publishing. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to verify OSL publishing. Missing OSL id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const oslId = payload.id;

    console.log(`[taskQueueWorker.js] Verifying publishing status for OSL with id=${oslId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Check if the OSL has been published
    const { data: oslRecord, error: fetchError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, shopify_uploaded_at')
      .eq('id', oslId)
      .maybeSingle();

    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error fetching OSL record: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching OSL record for id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to verify OSL publishing. Database error when retrieving OSL record.",
        error: fetchError.message
      });
      return;
    }

    if (!oslRecord) {
      const errMsg = `[taskQueueWorker.js] OSL record not found for id=${oslId}`;
      console.error(errMsg);
      await logError(errMsg, `OSL record not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to verify OSL publishing. OSL record not found.",
        error: 'OSL record not found'
      });
      return;
    }

    // Check if the OSL has been published
    if (oslRecord.shopify_uploaded_at) {
      console.log(`[taskQueueWorker.js] OSL ${oslId} has been published at ${oslRecord.shopify_uploaded_at}`);
      await updateTaskStatus(task.id, 'completed', {
        message: "Success! OSL has been published to Shopify.",
        shopify_uploaded_at: oslRecord.shopify_uploaded_at
      });
      return;
    }

    // If we get here, the OSL has not been published yet
    console.log(`[taskQueueWorker.js] OSL ${oslId} has not been published yet`);

    // Check if we've exceeded the maximum number of retries
    const { data: taskRecord, error: taskError } = await supabase
      .from('t_task_queue')
      .select('fails')
      .eq('id', task.id)
      .maybeSingle();

    if (taskError) {
      const errMsg = `[taskQueueWorker.js] Error fetching task record: ${taskError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching task record for id=${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to verify OSL publishing. Database error when retrieving task record.",
        error: taskError.message
      });
      return;
    }

    const fails = taskRecord?.fails || 0;
    if (fails >= 5) {
      console.log(`[taskQueueWorker.js] Exceeded maximum number of retries (${fails}) for task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to verify OSL publishing. Exceeded maximum number of retries.",
        fails: fails
      });
      return;
    }

    // Reschedule the task for 5 minutes later
    const scheduledAt = new Date();
    scheduledAt.setMinutes(scheduledAt.getMinutes() + 5);

    const { error: updateError } = await supabase
      .from('t_task_queue')
      .update({
        status: 'pending',
        fails: fails + 1,
        scheduled_at: scheduledAt.toISOString()
      })
      .eq('id', task.id);

    if (updateError) {
      const errMsg = `[taskQueueWorker.js] Error rescheduling task: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Rescheduling task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to reschedule verification task. Database error.",
        error: updateError.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Rescheduled task ${task.id} for ${scheduledAt.toISOString()}`);
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to verify OSL publishing due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a check_discs_for_future_osl_publish task
async function processCheckDiscsForFutureOslPublishTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process future OSL disc check task. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.osl_id) {
      const errMsg = `[taskQueueWorker.js] Missing osl_id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process future OSL disc check task. Missing OSL id in payload.",
        error: 'Missing osl_id in payload'
      });
      return;
    }

    const oslId = payload.osl_id;
    const originalScheduledAt = payload.original_scheduled_at;

    console.log(`[taskQueueWorker.js] Checking discs for future OSL publish, OSL id=${oslId}, originally scheduled for ${originalScheduledAt}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Find all discs related to this OSL
    console.log(`[taskQueueWorker.js] Finding discs with order_sheet_line_id=${oslId}...`);
    const { data: relatedDiscs, error: fetchDiscError } = await supabase
      .from('t_discs')
      .select('id')
      .eq('order_sheet_line_id', oslId);

    if (fetchDiscError) {
      const errMsg = `[taskQueueWorker.js] Error fetching related discs for OSL ${oslId}: ${fetchDiscError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to fetch related discs for OSL.",
        error: fetchDiscError.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Found ${relatedDiscs ? relatedDiscs.length : 0} discs related to OSL ${oslId}`);

    // Enqueue check_if_disc_is_ready tasks for each related disc
    let enqueuedCount = 0;
    let errorCount = 0;
    const now = new Date();

    if (relatedDiscs && relatedDiscs.length > 0) {
      for (const disc of relatedDiscs) {
        try {
          console.log(`[taskQueueWorker.js] Enqueueing check_if_disc_is_ready task for disc id=${disc.id}`);

          const { error: createTaskError } = await supabase
            .from('t_task_queue')
            .insert({
              task_type: 'check_if_disc_is_ready',
              payload: { id: disc.id },
              status: 'pending',
              scheduled_at: now.toISOString(), // Schedule for now
              created_at: now.toISOString(),
              enqueued_by: 'Future Pending OSL Publish Task'
            });

          if (createTaskError) {
            const errMsg = `[taskQueueWorker.js] Error creating check_if_disc_is_ready task for disc ${disc.id}: ${createTaskError.message}`;
            console.error(errMsg);
            await logError(errMsg, `Creating task for disc id=${disc.id}`);
            errorCount++;
          } else {
            enqueuedCount++;
          }
        } catch (err) {
          const errMsg = `[taskQueueWorker.js] Exception while creating check_if_disc_is_ready task for disc ${disc.id}: ${err.message}`;
          console.error(errMsg);
          await logError(errMsg, `Creating task for disc id=${disc.id}`);
          errorCount++;
        }
      }
    }

    // Mark the task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully enqueued disc ready checks for future OSL publish: ${enqueuedCount} tasks enqueued, ${errorCount} errors.`,
      success: true,
      osl_id: oslId,
      discs_found: relatedDiscs ? relatedDiscs.length : 0,
      tasks_enqueued: enqueuedCount,
      errors: errorCount,
      original_scheduled_at: originalScheduledAt
    });

    console.log(`[taskQueueWorker.js] Task ${task.id} completed. Enqueued ${enqueuedCount} check_if_disc_is_ready tasks for future OSL ${oslId}.`);
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process future OSL disc check task due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a mps_price_verified_osl_uploaded_look_for_discs task
async function processMpsPriceVerifiedOslUploadedLookForDiscsTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process OSL uploaded look for discs task. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process OSL uploaded look for discs task. Missing OSL id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const oslId = payload.id;

    console.log(`[taskQueueWorker.js] Looking for discs for uploaded OSL id=${oslId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Find all discs with this OSL ID that haven't been uploaded to Shopify and are ready_new = TRUE
    const { data: discRecords, error: fetchError } = await supabase
      .from('t_discs')
      .select('id, order_sheet_line_id')
      .eq('order_sheet_line_id', oslId)
      .is('shopify_uploaded_at', null)
      .eq('ready_new', true);

    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error fetching disc records: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching disc records for order_sheet_line_id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process OSL uploaded look for discs task. Database error when retrieving disc records.",
        error: fetchError.message
      });
      return;
    }

    if (!discRecords || discRecords.length === 0) {
      console.log(`[taskQueueWorker.js] No disc records found for OSL id=${oslId} that need processing`);
      await updateTaskStatus(task.id, 'completed', {
        message: "No disc records found that need processing.",
        osl_id: oslId
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Found ${discRecords.length} disc records for OSL id=${oslId} that need processing`);

    // Process each disc record
    let processedCount = 0;
    let errorCount = 0;

    for (const disc of discRecords) {
      try {
        // Set ready_new to false
        console.log(`[taskQueueWorker.js] Setting ready_new to FALSE for disc id=${disc.id}`);
        const { error: updateError } = await supabase
          .from('t_discs')
          .update({ ready_new: false })
          .eq('id', disc.id);

        if (updateError) {
          const errMsg = `[taskQueueWorker.js] Error setting ready_new to FALSE for disc id=${disc.id}: ${updateError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Updating disc id=${disc.id}`);
          errorCount++;
          continue;
        }

        processedCount++;
        console.log(`[taskQueueWorker.js] Successfully set ready_new to FALSE for disc id=${disc.id}`);
      } catch (err) {
        const errMsg = `[taskQueueWorker.js] Exception while processing disc id=${disc.id}: ${err.message}`;
        console.error(errMsg);
        await logError(errMsg, `Processing disc id=${disc.id}`);
        errorCount++;
      }
    }

    console.log(`[taskQueueWorker.js] Completed processing ${processedCount} disc records with ${errorCount} errors`);
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully processed ${processedCount} disc records with ${errorCount} errors.`,
      osl_id: oslId,
      processed_count: processedCount,
      error_count: errorCount
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process OSL uploaded look for discs task due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a toggle_osl_ready_button task
async function processToggleOslReadyButtonTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to toggle OSL ready button. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to toggle OSL ready button. Missing OSL id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const oslId = payload.id;

    console.log(`[taskQueueWorker.js] Toggling ready_button for OSL id=${oslId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Check if the OSL exists and hasn't been uploaded yet
    const { data: oslRecord, error: fetchError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, shopify_uploaded_at')
      .eq('id', oslId)
      .maybeSingle();

    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error fetching OSL record: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching OSL record for id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to toggle OSL ready button. Database error when retrieving OSL record.",
        error: fetchError.message
      });
      return;
    }

    if (!oslRecord) {
      const errMsg = `[taskQueueWorker.js] OSL record not found for id=${oslId}`;
      console.error(errMsg);
      await logError(errMsg, `OSL record not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to toggle OSL ready button. OSL record not found.",
        error: 'OSL record not found'
      });
      return;
    }

    // If the OSL has already been uploaded, skip toggling the ready button
    if (oslRecord.shopify_uploaded_at) {
      console.log(`[taskQueueWorker.js] OSL ${oslId} has already been uploaded at ${oslRecord.shopify_uploaded_at}. Skipping toggle.`);
      await updateTaskStatus(task.id, 'completed', {
        message: "OSL has already been uploaded to Shopify. Skipping toggle.",
        shopify_uploaded_at: oslRecord.shopify_uploaded_at
      });
      return;
    }

    // Set ready_button to false
    console.log(`[taskQueueWorker.js] Setting ready_button to FALSE for OSL id=${oslId}`);
    const { error: updateError1 } = await supabase
      .from('t_order_sheet_lines')
      .update({ ready_button: false })
      .eq('id', oslId);

    if (updateError1) {
      const errMsg = `[taskQueueWorker.js] Error setting ready_button to FALSE for OSL id=${oslId}: ${updateError1.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating OSL id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to set ready_button to FALSE for OSL.",
        error: updateError1.message
      });
      return;
    }

    // Wait 10 seconds to give more time for processing
    console.log(`[taskQueueWorker.js] Waiting 10 seconds before setting ready_button back to TRUE for OSL id=${oslId}`);
    await new Promise(resolve => setTimeout(resolve, 10000));

    // Set ready_button to true
    console.log(`[taskQueueWorker.js] Setting ready_button to TRUE for OSL id=${oslId}`);
    const { error: updateError2 } = await supabase
      .from('t_order_sheet_lines')
      .update({ ready_button: true })
      .eq('id', oslId);

    if (updateError2) {
      const errMsg = `[taskQueueWorker.js] Error setting ready_button to TRUE for OSL id=${oslId}: ${updateError2.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating OSL id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to set ready_button to TRUE for OSL.",
        error: updateError2.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Successfully toggled ready_button for OSL id=${oslId}`);
    await updateTaskStatus(task.id, 'completed', {
      message: "Successfully toggled ready_button for OSL.",
      osl_id: oslId
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to toggle OSL ready button due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process an update_osl_after_publish task
async function processUpdateOslAfterPublishTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update OSL after publish. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update OSL after publish. Missing OSL id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const oslId = payload.id;
    const shopifyUploadedAt = payload.shopify_uploaded_at || new Date().toISOString();
    const shopifyProductUploadedNotes = payload.shopify_product_uploaded_notes || "Updated by worker task";

    console.log(`[taskQueueWorker.js] Updating OSL with id=${oslId} after publish`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Update the OSL record with a simple update
    // Break it down into smaller operations to avoid timeout
    console.log(`[taskQueueWorker.js] Updating shopify_uploaded_at for OSL id=${oslId}`);
    const { error: updateError1 } = await supabase
      .from('t_order_sheet_lines')
      .update({ shopify_uploaded_at: shopifyUploadedAt })
      .eq('id', oslId);

    if (updateError1) {
      const errMsg = `[taskQueueWorker.js] Error updating shopify_uploaded_at for OSL id=${oslId}: ${updateError1.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating OSL id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update shopify_uploaded_at for OSL.",
        error: updateError1.message
      });
      return;
    }

    // Wait a moment before the next update
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log(`[taskQueueWorker.js] Updating shopify_product_uploaded_notes for OSL id=${oslId}`);
    const { error: updateError2 } = await supabase
      .from('t_order_sheet_lines')
      .update({ shopify_product_uploaded_notes: shopifyProductUploadedNotes })
      .eq('id', oslId);

    if (updateError2) {
      const errMsg = `[taskQueueWorker.js] Error updating shopify_product_uploaded_notes for OSL id=${oslId}: ${updateError2.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating OSL id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update shopify_product_uploaded_notes for OSL.",
        error: updateError2.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Successfully updated OSL id=${oslId} after publish`);
    await updateTaskStatus(task.id, 'completed', {
      message: "Success! OSL updated after publish.",
      osl_id: oslId
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to update OSL after publish due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a plastic_price_verified_work_through_mps_to_find_osls_and_discs_to_upload task
async function processPlasticPriceVerifiedWorkThroughMpsTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process plastic price verified task. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process plastic price verified task. Missing plastic id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const plasticId = payload.id;

    console.log(`[taskQueueWorker.js] Processing plastic price verified task for plastic id=${plasticId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Find all MPS records with this plastic ID, stamp_id = 38, and shopify_collection_uploaded_at not null
    const { data: mpsRecords, error: fetchError } = await supabase
      .from('t_mps')
      .select('id, plastic_id, stamp_id, shopify_collection_uploaded_at')
      .eq('plastic_id', plasticId)
      .eq('stamp_id', 38)
      .not('shopify_collection_uploaded_at', 'is', null);

    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error fetching MPS records: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching MPS records for plastic_id=${plasticId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process plastic price verified task. Database error when retrieving MPS records.",
        error: fetchError.message
      });
      return;
    }

    if (!mpsRecords || mpsRecords.length === 0) {
      console.log(`[taskQueueWorker.js] No MPS records found for plastic id=${plasticId} with stamp_id=38 and shopify_collection_uploaded_at not null`);
      await updateTaskStatus(task.id, 'completed', {
        message: "No matching MPS records found.",
        plastic_id: plasticId
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Found ${mpsRecords.length} MPS records for plastic id=${plasticId} with stamp_id=38 and shopify_collection_uploaded_at not null`);

    // Process each MPS record
    let processedCount = 0;
    let errorCount = 0;

    for (const mps of mpsRecords) {
      try {
        // Create a task for each MPS record
        console.log(`[taskQueueWorker.js] Creating mps_price_verified_try_upload_osls task for MPS id=${mps.id}`);

        const { error: createTaskError } = await supabase
          .from('t_task_queue')
          .insert({
            task_type: 'mps_price_verified_try_upload_osls',
            payload: { id: mps.id },
            status: 'pending',
            scheduled_at: new Date().toISOString(),
            created_at: new Date().toISOString(),
            enqueued_by: task.task_type
          });

        if (createTaskError) {
          const errMsg = `[taskQueueWorker.js] Error creating task for MPS id=${mps.id}: ${createTaskError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Creating task for MPS`);
          errorCount++;
        } else {
          console.log(`[taskQueueWorker.js] Successfully created task for MPS id=${mps.id}`);
          processedCount++;
        }
      } catch (err) {
        const errMsg = `[taskQueueWorker.js] Exception while processing MPS id=${mps.id}: ${err.message}`;
        console.error(errMsg);
        await logError(errMsg, `Processing MPS id=${mps.id}`);
        errorCount++;
      }
    }

    console.log(`[taskQueueWorker.js] Completed creating ${processedCount} MPS tasks with ${errorCount} errors`);
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully created ${processedCount} MPS tasks with ${errorCount} errors.`,
      plastic_id: plasticId,
      processed_count: processedCount,
      error_count: errorCount
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process plastic price verified task due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a reconcile_clear_count_from_shopify_for_sold_disc task
async function processClearShopifyCountForSoldDiscTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process clear Shopify count task. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing disc id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process clear Shopify count task. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    const discId = payload.id;
    let shopifyProductId = payload.shopify_product_id;

    // Get the variant information from the database
    console.log(`[taskQueueWorker.js] Fetching variant information from database for disc id=${discId}`);

    // First, check if the disc exists in Shopify
    const { data: discExists, error: existsError } = await supabase
      .from('t_discs')
      .select('shopify_uploaded_at')
      .eq('id', discId)
      .single();

    if (existsError) {
      const errMsg = `[taskQueueWorker.js] Error checking if disc exists: ${existsError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Checking if disc exists for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process clear Shopify count task. Error checking if disc exists.",
        error: existsError.message
      });
      return;
    }

    if (!discExists || !discExists.shopify_uploaded_at) {
      console.log(`[taskQueueWorker.js] Disc id=${discId} has not been uploaded to Shopify, skipping inventory update`);
      await updateTaskStatus(task.id, 'completed', {
        message: "Disc has not been uploaded to Shopify, no inventory update needed.",
        disc_id: discId
      });
      return;
    }

    // Get the variant information from the reconciliation view
    const { data: discData, error: discError } = await supabase
      .from('v_reconcile_d_to_shopify')
      .select('shopify_product_id, shopify_variant_id, shopify_variant_inventory_item_id')
      .eq('disc_id', discId)
      .eq('issue_description', 'Sold disc still showing up on Shopify.');

    if (discError) {
      const errMsg = `[taskQueueWorker.js] Error fetching variant information for disc id=${discId}: ${discError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching variant information for disc id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process clear Shopify count task. Error fetching variant information.",
        error: discError.message
      });
      return;
    }

    if (!discData || discData.length === 0) {
      console.log(`[taskQueueWorker.js] No variant information found in reconciliation view for disc id=${discId}`);

      // Try to get the variant information directly from the disc record
      const { data: directDiscData, error: directDiscError } = await supabase
        .from('t_discs')
        .select('shopify_product_id, shopify_variant_id, shopify_variant_inventory_item_id')
        .eq('id', discId)
        .single();

      if (directDiscError || !directDiscData || !directDiscData.shopify_variant_inventory_item_id) {
        const errMsg = `[taskQueueWorker.js] No Shopify variant information found for disc id=${discId}`;
        console.error(errMsg);
        await logError(errMsg, `Fetching variant information for disc id=${discId}`);
        await updateTaskStatus(task.id, 'completed', {
          message: "No Shopify variant information found. The disc may have already been removed from Shopify.",
          disc_id: discId
        });
        return;
      }

      // Use the direct disc data
      discData = [directDiscData];
    }

    // If we have multiple variants, log it but proceed with all of them
    if (discData.length > 1) {
      console.log(`[taskQueueWorker.js] Found ${discData.length} variants for disc id=${discId}, processing all of them`);
    }

    // Process each variant
    const results = [];
    const errors = [];
    let successCount = 0;
    let errorCount = 0;

    for (const variant of discData) {
      // Get the variant ID and inventory item ID
      const variantShopifyProductId = shopifyProductId || variant.shopify_product_id;
      const shopifyVariantId = variant.shopify_variant_id;
      const shopifyVariantInventoryItemId = variant.shopify_variant_inventory_item_id;

      if (!shopifyVariantInventoryItemId) {
        const errMsg = `[taskQueueWorker.js] No shopify_variant_inventory_item_id found for variant`;
        console.error(errMsg);
        await logError(errMsg, `Processing variant for disc id=${discId}`);
        errors.push({
          message: "No shopify_variant_inventory_item_id found for variant",
          variant
        });
        errorCount++;
        continue;
      }

      console.log(`[taskQueueWorker.js] Setting inventory to zero for disc id=${discId} with shopify_variant_inventory_item_id=${shopifyVariantInventoryItemId}`);

      try {
        // Import the Shopify GraphQL utility
        const { setInventoryItemToZero } = await import('./shopifyGraphQL.js');

        // Set the inventory to zero
        const result = await setInventoryItemToZero(shopifyVariantInventoryItemId);

        console.log(`[taskQueueWorker.js] Successfully set inventory to zero for disc id=${discId} with shopify_variant_inventory_item_id=${shopifyVariantInventoryItemId}`);
        console.log(`[taskQueueWorker.js] Result: ${JSON.stringify(result)}`);

        results.push({
          shopify_product_id: variantShopifyProductId,
          shopify_variant_id: shopifyVariantId,
          shopify_variant_inventory_item_id: shopifyVariantInventoryItemId,
          result
        });
        successCount++;
      } catch (variantErr) {
        const errMsg = `[taskQueueWorker.js] Error setting inventory to zero for variant: ${variantErr.message}`;
        console.error(errMsg);
        await logError(errMsg, `Setting inventory to zero for disc id=${discId}`);
        errors.push({
          message: "Error setting inventory to zero for variant",
          error: variantErr.message,
          shopify_product_id: variantShopifyProductId,
          shopify_variant_id: shopifyVariantId,
          shopify_variant_inventory_item_id: shopifyVariantInventoryItemId
        });
        errorCount++;
      }
    }

    // Update task status based on results
    if (errorCount === 0) {
      await updateTaskStatus(task.id, 'completed', {
        message: `Successfully set Shopify inventory to zero for all ${successCount} variants of disc id=${discId}`,
        disc_id: discId,
        success_count: successCount,
        results
      });
    } else if (successCount === 0) {
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to set Shopify inventory to zero for any variants",
        disc_id: discId,
        error_count: errorCount,
        errors
      });
    } else {
      await updateTaskStatus(task.id, 'completed', {
        message: `Partially successful: Set inventory to zero for ${successCount} variants, failed for ${errorCount} variants`,
        disc_id: discId,
        success_count: successCount,
        error_count: errorCount,
        results,
        errors
      });
    }
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process clear Shopify count task due to an unexpected error.",
      error: err.message,
      disc_id: discId
    });
  }
}

// Function to release stale locks
async function releaseStaleTaskLocks(timeoutMinutes = 30) {
  console.log(`[taskQueueWorker.js] Releasing stale task locks (timeout: ${timeoutMinutes} minutes)...`);

  try {
    const { data, error } = await supabase.rpc('release_stale_task_locks', {
      timeout_minutes: timeoutMinutes
    });

    if (error) {
      const errMsg = `[taskQueueWorker.js] Error releasing stale task locks: ${error.message}`;
      console.error(errMsg);
      await logError(errMsg, 'Releasing stale task locks');
      return 0;
    }

    console.log(`[taskQueueWorker.js] Released ${data} stale task locks`);
    return data;
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while releasing stale task locks: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, 'Releasing stale task locks');
    return 0;
  }
}

/**
 * Process a task to reconcile OSL quantities in Veeqo
 * @param {Object} task - The task to process
 */
async function processReconcileOslToVeeqoTask(task) {
  console.log(`[taskQueueWorker.js] Processing reconcile OSL to Veeqo task ${task.id}`);

  try {
    // Extract OSL ID and quantities from the payload
    const oslId = task.payload.id;
    const localQty = task.payload.local_qty;
    const veeqoQty = task.payload.veeqo_qty;

    if (!oslId) {
      const errMsg = `[taskQueueWorker.js] No OSL ID in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process reconcile OSL to Veeqo task. No OSL ID in payload.",
        error: 'No OSL ID in payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Reconciling OSL ID=${oslId} with local_qty=${localQty} and veeqo_qty=${veeqoQty}`);

    // First, get the OSL record to check if it has a veeqo_id
    const { data: oslData, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, veeqo_id')
      .eq('id', oslId)
      .single();

    if (oslError) {
      const errMsg = `[taskQueueWorker.js] Error getting OSL ID=${oslId}: ${oslError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Getting OSL ID=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process reconcile OSL to Veeqo task. Error getting OSL.",
        error: oslError.message,
        osl_id: oslId
      });
      return;
    }

    if (!oslData) {
      const errMsg = `[taskQueueWorker.js] No OSL found with ID=${oslId}`;
      console.error(errMsg);
      await logError(errMsg, `Getting OSL ID=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process reconcile OSL to Veeqo task. No OSL found.",
        error: 'No OSL found',
        osl_id: oslId
      });
      return;
    }

    if (!oslData.veeqo_id) {
      const errMsg = `[taskQueueWorker.js] OSL ID=${oslId} has no veeqo_id`;
      console.error(errMsg);
      await logError(errMsg, `Getting OSL ID=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process reconcile OSL to Veeqo task. OSL has no veeqo_id.",
        error: 'OSL has no veeqo_id',
        osl_id: oslId
      });
      return;
    }

    // Update the t_inv_osl table to trigger the Veeqo update
    const { data: updateData, error: updateError } = await supabase
      .from('t_inv_osl')
      .update({
        veeqo_qty_updated_at: new Date().toISOString()
      })
      .eq('id', oslId)
      .select();

    if (updateError) {
      const errMsg = `[taskQueueWorker.js] Error updating t_inv_osl for ID=${oslId}: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating t_inv_osl ID=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process reconcile OSL to Veeqo task. Error updating t_inv_osl.",
        error: updateError.message,
        osl_id: oslId
      });
      return;
    }

    if (!updateData || updateData.length === 0) {
      const errMsg = `[taskQueueWorker.js] No t_inv_osl record found with ID=${oslId}`;
      console.error(errMsg);
      await logError(errMsg, `Updating t_inv_osl ID=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process reconcile OSL to Veeqo task. No t_inv_osl record found.",
        error: 'No t_inv_osl record found',
        osl_id: oslId
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Successfully updated t_inv_osl ID=${oslId} to trigger Veeqo update`);

    // Update task status to completed
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully updated t_inv_osl ID=${oslId} to trigger Veeqo update`,
      osl_id: oslId,
      local_qty: localQty,
      veeqo_qty: veeqoQty
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process reconcile OSL to Veeqo task due to an unexpected error.",
      error: err.message
    });
  }
}

// Main function to run the worker
async function main() {
  console.log('[taskQueueWorker.js] Starting task queue worker...');

  try {
    // Release any stale locks before starting
    await releaseStaleTaskLocks();
    // Fetch pending tasks (up to 100 at a time)
    const pendingTasks = await fetchPendingTasks(100);

    if (pendingTasks.length === 0) {
      console.log('[taskQueueWorker.js] No pending tasks to process. Exiting.');
      return;
    }

    console.log(`[taskQueueWorker.js] Found ${pendingTasks.length} pending tasks to process.`);

    // Process each task
    for (const task of pendingTasks) {
      if (task.task_type === 'verify_t_images_image') {
        await processVerifyImageTask(task);
      } else if (task.task_type === 'insert_new_t_images_record') {
        await processInsertImageTask(task);
      } else if (task.task_type === 'delete_t_images_record') {
        await processDeleteImageTask(task);
      } else if (task.task_type === 'check_if_disc_ready_to_publish') {
        await processCheckDiscReadyTask(task);
      } else if (task.task_type === 'verify_disc_image') {
        await processVerifyDiscImageTask(task);
      } else if (task.task_type === 'clear_disc_verification') {
        await processClearDiscVerificationTask(task);
      } else if (task.task_type === 'check_if_disc_is_ready') {
        await processCheckDiscIsReadyTask(task);
      } else if (task.task_type === 'publish_disc' || task.task_type === 'publish_product_disc') {
        await processPublishDiscTask(task);
      } else if (task.task_type === 'publish_product_disc_verify') {
        await processPublishProductDiscVerifyTask(task);
      } else if (task.task_type === 'publish_product_osl') {
        await processPublishProductOslTask(task);
      } else if (task.task_type === 'publish_product_osl_verify') {
        await processPublishProductOslVerifyTask(task);
      } else if (task.task_type === 'generate_disc_title_pull_and_handle') {
        await processGenerateDiscTitlePullAndHandleTask(task);
      } else if (task.task_type === 'generate_mps_fields') {
        await processGenerateMpsFieldsTask(task);
      } else if (task.task_type === 'generate_osl_fields') {
        await processGenerateOslFieldsTask(task);
      } else if (task.task_type === 'stamp_update_downstream_generate_mps_fields') {
        await processStampUpdateDownstreamGenerateMpsFieldsTask(task);
      } else if (task.task_type === 'mold_update_downstream_generate_mps_fields') {
        await processMoldUpdateDownstreamGenerateMpsFieldsTask(task);
      } else if (task.task_type === 'plastic_update_downstream_generate_mps_fields') {
        await processPlasticUpdateDownstreamGenerateMpsFieldsTask(task);
      } else if (task.task_type === 'mps_g_code_update_downstream_generate_osl_fields') {
        await processMpsGCodeUpdateDownstreamGenerateOslFieldsTask(task);
      } else if (task.task_type === 'update_veeqo_sdasin_qty') {
        await processUpdateVeeqoSdasinQtyTask(task);
      } else if (task.task_type === 'update_veeqo_osl_qty') {
        await processUpdateVeeqoOslQtyTask(task);
      } else if (task.task_type === 'set_disc_carry_cost') {
        await processSetDiscCarryCostTask(task);
      } else if (task.task_type === 'match_disc_to_asins') {
        await processMatchDiscToAsinsTask(task);
      } else if (task.task_type === 'match_disc_to_osl') {
        await processMatchDiscToOslTask(task);
      } else if (task.task_type === 'mps_price_verified_try_upload_osls') {
        await processMpsPriceVerifiedTryUploadOslsTask(task);
      } else if (task.task_type === 'mps_price_verified_osl_uploaded_look_for_discs') {
        await processMpsPriceVerifiedOslUploadedLookForDiscsTask(task);
      } else if (task.task_type === 'toggle_osl_ready_button') {
        await processToggleOslReadyButtonTask(task);
      } else if (task.task_type === 'plastic_price_verified_work_through_mps_to_find_osls_and_discs_to_upload') {
        await processPlasticPriceVerifiedWorkThroughMpsTask(task);
      } else if (task.task_type === 'reconcile_clear_count_from_shopify_for_sold_disc') {
        await processClearShopifyCountForSoldDiscTask(task);
      } else if (task.task_type === 'reconcile_osl_to_veeqo') {
        await processReconcileOslToVeeqoTask(task);
      } else if (task.task_type === 'update_osl_after_publish') {
        await processUpdateOslAfterPublishTask(task);
      } else if (task.task_type === 'check_if_mold_is_ready') {
        await processCheckIfMoldIsReadyTask(task);
      } else if (task.task_type === 'publish_mold_collection') {
        await processPublishMoldCollectionTask(task);
      } else if (task.task_type === 'sdasin_updated_find_discs_to_match') {
        await processSdasinUpdatedFindDiscsToMatchTask(task, { supabase, updateTaskStatus, logError });
      } else if (task.task_type === 'disc_sold_update_veeqo_qty_by_d_sku') {
        await processDiscSoldUpdateVeeqoQtyByDSkuTask(task);
      } else if (task.task_type === 'disc_unsold_update_veeqo_qty_by_d_sku') {
        await processDiscUnsoldUpdateVeeqoQtyByDSkuTask(task);
      } else if (task.task_type === 'check_if_osl_is_ready') {
        await processCheckOslIsReadyTask(task);
      } else if (task.task_type === 'osl_uploaded_check_if_related_discs_are_ready') {
        await processOslUploadedCheckRelatedDiscsTask(task);
      } else if (task.task_type === 'mps_published_check_related_osls_for_ready') {
        await processMpsPublishedCheckRelatedOslsTask(task);
      } else if (task.task_type === 'check_if_plastic_is_ready') {
        await processCheckIfPlasticIsReadyTask(task);
      } else if (task.task_type === 'publish_plastic_collection') {
        await processPublishPlasticCollectionTask(task);
      } else if (task.task_type === 'mps_price_verified_try_upload_osls') {
        await processMpsPriceVerifiedTryUploadOslsTask(task);
      } else if (task.task_type === 'plastics_uploaded_check_related_mps_for_ready') {
        await processPlasticsUploadedCheckRelatedMpsTask(task);
      } else if (task.task_type === 'check_if_mps_is_ready') {
        await processCheckIfMpsIsReadyTask(task);
      } else if (task.task_type === 'mps_is_ready_so_publish') {
        await processMpsIsReadySoPublishTask(task);
      } else if (task.task_type === 'd_sold_or_unsold_update_sdasin_inv') {
        await processDiscSoldOrUnsoldUpdateSdasinInvTask(task);
      } else if (task.task_type === 'disc_sold_or_unsold') {
        await processDiscSoldOrUnsoldTask(task);
      } else if (task.task_type === 'd_sold_or_unsold_update_osl_inv') {
        await processDiscSoldOrUnsoldUpdateOslInvTask(task);
      } else if (task.task_type === 'd_sold_or_unsold_udpate_shopify_directly') {
        await processDiscSoldOrUnsoldUpdateShopifyDirectlyTask(task);
      } else if (task.task_type === 'new_t_discs_record') {
        await processNewTDiscsRecordTask(task, { supabase, updateTaskStatus, logError });
      } else if (task.task_type === 'osl_inserted') {
        await processOslInsertedTask(task, { supabase, updateTaskStatus, logError });
      } else if (task.task_type === 'osl_updated') {
        await processOslUpdatedTask(task, { supabase, updateTaskStatus, logError });
      } else if (task.task_type === 'osl_inserted_create_inv_osl') {
        await processOslInsertedCreateInvOslTask(task, { supabase, updateTaskStatus, logError });
      } else if (task.task_type === 'set_inv_osl_to_0') {
        await processSetInvOslTo0Task(task, { supabase, updateTaskStatus, logError });
      } else if (task.task_type === 'osl_updated_unlink_discs') {
        await processOslUpdatedUnlinkDiscsTask(task, { supabase, updateTaskStatus, logError });
      } else if (task.task_type === 'match_osl_to_discs') {
        await processMatchOslToDiscsTask(task, { supabase, updateTaskStatus, logError });
      } else if (task.task_type === 'rename_and_upload_images') {
        await processRenameAndUploadImagesTask(task, { supabase, updateTaskStatus, logError });
      } else if (task.task_type === 'check_discs_for_future_osl_publish') {
        await processCheckDiscsForFutureOslPublishTask(task);
      } else if (task.task_type === 'download_informed_reports') {
        // Process download_informed_reports task
        console.log(`[taskQueueWorker.js] Processing download_informed_reports task ${task.id}`);
        await updateTaskStatus(task.id, 'processing');

        try {
          const result = await informedTaskHandler.handleDownloadInformedReports(task);
          await updateTaskStatus(task.id, result.success ? 'completed' : 'error', result);
        } catch (error) {
          console.error(`[taskQueueWorker.js] Error processing download_informed_reports task: ${error.message}`);
          await updateTaskStatus(task.id, 'error', {
            message: `Error processing download_informed_reports task: ${error.message}`,
            error: error.message
          });
        }
      } else if (task.task_type === 'import_informed_reports') {
        // Process import_informed_reports task
        console.log(`[taskQueueWorker.js] Processing import_informed_reports task ${task.id}`);
        await updateTaskStatus(task.id, 'processing');

        try {
          const result = await informedTaskHandler.handleImportInformedReports(task);
          await updateTaskStatus(task.id, result.success ? 'completed' : 'error', result);
        } catch (error) {
          console.error(`[taskQueueWorker.js] Error processing import_informed_reports task: ${error.message}`);
          await updateTaskStatus(task.id, 'error', {
            message: `Error processing import_informed_reports task: ${error.message}`,
            error: error.message
          });
        }
      } else if (task.task_type === 'run_informed_process') {
        // Process run_informed_process task
        console.log(`[taskQueueWorker.js] Processing run_informed_process task ${task.id}`);
        await updateTaskStatus(task.id, 'processing');

        try {
          const result = await informedTaskHandler.handleRunInformedProcess(task);
          await updateTaskStatus(task.id, result.success ? 'completed' : 'error', result);
        } catch (error) {
          console.error(`[taskQueueWorker.js] Error processing run_informed_process task: ${error.message}`);
          await updateTaskStatus(task.id, 'error', {
            message: `Error processing run_informed_process task: ${error.message}`,
            error: error.message
          });
        }
      } else {
        console.log(`[taskQueueWorker.js] Skipping unsupported task type: ${task.task_type}`);
        // Release the lock by setting status back to 'pending'
        await updateTaskStatus(task.id, 'pending', {
          message: `Task type '${task.task_type}' not supported by this worker. Lock released.`,
          skipped_at: new Date().toISOString()
        });
      }
    }

    console.log('[taskQueueWorker.js] All tasks processed. Exiting.');
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception in main function: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, 'Main function');
  }
}

// Function to process a check_if_mold_is_ready task
async function processCheckIfMoldIsReadyTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if mold is ready. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if mold is ready. Missing mold id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const moldId = payload.id;

    console.log(`[taskQueueWorker.js] Checking if mold with id=${moldId} is ready`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Get the mold record
    const { data: moldRecord, error: moldError } = await supabase
      .from('t_molds')
      .select('*, t_brands!inner(shopify_collection_created_at)')
      .eq('id', moldId)
      .maybeSingle();

    if (moldError) {
      const errMsg = `[taskQueueWorker.js] Error fetching mold record: ${moldError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching mold record for id=${moldId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if mold is ready. Database error when retrieving mold record.",
        error: moldError.message
      });
      return;
    }

    if (!moldRecord) {
      const errMsg = `[taskQueueWorker.js] Mold record not found for id=${moldId}`;
      console.error(errMsg);
      await logError(errMsg, `Mold record not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if mold is ready. Mold record not found.",
        error: 'Mold record not found'
      });
      return;
    }

    // Get the stats for this mold
    const { data: statsRecord, error: statsError } = await supabase
      .from('v_stats_by_mold')
      .select('discs_in_stock_total')
      .eq('id', moldId)
      .maybeSingle();

    if (statsError) {
      const errMsg = `[taskQueueWorker.js] Error fetching stats record: ${statsError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching stats record for mold id=${moldId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if mold is ready. Database error when retrieving stats record.",
        error: statsError.message
      });
      return;
    }

    // Check if at least one image is verified
    const { data: imageRecords, error: imageError } = await supabase
      .from('t_images')
      .select('id')
      .eq('table_name', 't_molds')
      .eq('record_id', moldId)
      .eq('image_verified', true);

    if (imageError) {
      const errMsg = `[taskQueueWorker.js] Error fetching image records: ${imageError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching image records for mold id=${moldId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if mold is ready. Database error when retrieving image records.",
        error: imageError.message
      });
      return;
    }

    // Check all the readiness rules
    const brokenRules = [];
    const discsInStock = statsRecord?.discs_in_stock_total || 0;

    // Shopify Collection
    if (moldRecord.shopify_collection_created_at !== null) {
      // If collection already published, skip all checks
      console.log(`[taskQueueWorker.js] Mold ${moldId} already has a Shopify collection created at ${moldRecord.shopify_collection_created_at}`);
      await updateTaskStatus(task.id, 'completed', {
        message: "Mold already has a Shopify collection created.",
        mold_id: moldId,
        shopify_collection_created_at: moldRecord.shopify_collection_created_at
      });
      return;
    }

    // Brand's Shopify Collection
    if (!moldRecord.t_brands?.shopify_collection_created_at) {
      brokenRules.push("Brand's Shopify collection not created");
    }

    // Core fields
    if (!moldRecord.mold || moldRecord.mold.trim() === '') {
      brokenRules.push("mold (name) is empty");
    }

    if (moldRecord.brand_id === null) {
      brokenRules.push("brand_id is null");
    }

    if (!moldRecord.code || !/^[A-Z0-9]+$/.test(moldRecord.code)) {
      brokenRules.push("code must exist and match ^[A-Z0-9]+$ (uppercase letters or digits only)");
    }

    const validTypes = ['Putter', 'Midrange', 'Fairway Driver', 'Distance Driver'];
    if (!moldRecord.type || !validTypes.includes(moldRecord.type)) {
      brokenRules.push(`type must be one of: ${validTypes.join(', ')}`);
    }

    // Flight numbers
    if (moldRecord.speed === null) {
      brokenRules.push("speed is null");
    }

    if (moldRecord.glide === null) {
      brokenRules.push("glide is null");
    }

    if (moldRecord.turn === null) {
      brokenRules.push("turn is null");
    }

    if (moldRecord.fade === null) {
      brokenRules.push("fade is null");
    }

    // Description
    if (!moldRecord.description || moldRecord.description.trim() === '') {
      brokenRules.push("description is empty");
    }

    // Image verification
    if (!imageRecords || imageRecords.length === 0) {
      brokenRules.push("No verified images found");
    }

    // Determine if all rules are satisfied
    if (brokenRules.length === 0) {
      // All rules satisfied, enqueue publish_mold_collection task
      console.log(`[taskQueueWorker.js] All rules satisfied for mold ${moldId}, enqueueing publish_mold_collection task`);

      // Determine scheduled time (now or embargo_until, whichever is later)
      const now = new Date();
      let scheduledAt = now;

      if (moldRecord.embargo_until) {
        const embargoDate = new Date(moldRecord.embargo_until);
        if (embargoDate > now) {
          scheduledAt = embargoDate;
        }
      }

      // Create the publish_mold_collection task
      const { error: createTaskError } = await supabase
        .from('t_task_queue')
        .insert({
          task_type: 'publish_mold_collection',
          payload: { id: moldId },
          status: 'pending',
          scheduled_at: scheduledAt.toISOString(),
          created_at: now.toISOString()
        });

      if (createTaskError) {
        const errMsg = `[taskQueueWorker.js] Error creating publish_mold_collection task: ${createTaskError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Creating task for mold id=${moldId}`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to enqueue publish_mold_collection task.",
          error: createTaskError.message
        });
        return;
      }

      // Update t_molds.todo
      const todoMessage = `Enqueued for publish ${scheduledAt.toISOString()}`;
      const { error: updateError } = await supabase
        .from('t_molds')
        .update({ todo: todoMessage })
        .eq('id', moldId);

      if (updateError) {
        const errMsg = `[taskQueueWorker.js] Error updating t_molds.todo: ${updateError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Updating t_molds.todo for id=${moldId}`);
        // Continue anyway since the task was created
      }

      await updateTaskStatus(task.id, 'completed', {
        message: "All rules satisfied, publish_mold_collection task enqueued.",
        mold_id: moldId,
        scheduled_at: scheduledAt.toISOString(),
        todo: todoMessage
      });
    } else {
      // Some rules are broken, update t_molds.todo with the list
      console.log(`[taskQueueWorker.js] ${brokenRules.length} rules broken for mold ${moldId}`);

      // Format the todo message
      let todoMessage = brokenRules.join(', ');

      // Add CRITICAL prefix if discs_in_stock_total > 0
      if (discsInStock > 0) {
        todoMessage = `CRITICAL: ${todoMessage}`;
      }

      // Update t_molds.todo
      const { error: updateError } = await supabase
        .from('t_molds')
        .update({ todo: todoMessage })
        .eq('id', moldId);

      if (updateError) {
        const errMsg = `[taskQueueWorker.js] Error updating t_molds.todo: ${updateError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Updating t_molds.todo for id=${moldId}`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to update t_molds.todo with broken rules.",
          error: updateError.message
        });
        return;
      }

      await updateTaskStatus(task.id, 'completed', {
        message: "Some rules are broken, t_molds.todo updated.",
        mold_id: moldId,
        broken_rules: brokenRules,
        discs_in_stock: discsInStock,
        todo: todoMessage
      });
    }
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to check if mold is ready due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a publish_mold_collection task
async function processPublishMoldCollectionTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to publish mold collection. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to publish mold collection. Missing mold id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const moldId = payload.id;

    console.log(`[taskQueueWorker.js] Publishing mold collection for mold id=${moldId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Spawn the publishCollectionMold.js process
    const publishCollectionMoldPath = path.join(__dirname, 'publishCollectionMold.js');
    console.log(`[taskQueueWorker.js] Spawning process: node ${publishCollectionMoldPath} --id=${moldId}`);

    const publishProcess = spawn('node', [publishCollectionMoldPath, `--id=${moldId}`]);

    // Collect stdout and stderr
    let stdout = '';
    let stderr = '';

    publishProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[publishCollectionMold.js] ${output.trim()}`);
    });

    publishProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[publishCollectionMold.js] ${output.trim()}`);
    });

    // Handle process completion
    publishProcess.on('close', async (code) => {
      console.log(`[taskQueueWorker.js] publishCollectionMold.js process exited with code ${code}`);

      if (code === 0) {
        // Success
        console.log(`[taskQueueWorker.js] Successfully published mold collection for mold id=${moldId}`);
        await updateTaskStatus(task.id, 'completed', {
          message: "Success! Mold collection published to Shopify.",
          mold_id: moldId
        });
      } else {
        // Failure
        console.log(`[taskQueueWorker.js] Failed to publish mold collection for mold id=${moldId}`);
        console.log(`[taskQueueWorker.js] stdout: ${stdout}`);
        console.log(`[taskQueueWorker.js] stderr: ${stderr}`);

        // Extract error message from stdout if possible
        let errorMessage = "Unknown error";
        const errorLines = stdout.split('\n').filter(line => line.includes('ERROR:'));
        if (errorLines.length > 0) {
          errorMessage = errorLines[errorLines.length - 1].replace('ERROR:', '').trim();
        }

        await updateTaskStatus(task.id, 'error', {
          message: "Failed to publish mold collection.",
          error: errorMessage || stderr || `Process exited with code ${code}`,
          mold_id: moldId
        });
      }
    });

    // Handle process error
    publishProcess.on('error', async (err) => {
      const errMsg = `[taskQueueWorker.js] Error spawning publishCollectionMold.js process: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);

      await updateTaskStatus(task.id, 'error', {
        message: "Error spawning publishCollectionMold.js process. The mold collection was not published.",
        error: err.message,
        mold_id: moldId
      });
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to publish mold collection due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a disc_sold_update_veeqo_qty_by_d_sku task
async function processDiscSoldUpdateVeeqoQtyByDSkuTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update Veeqo quantity. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update Veeqo quantity. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;
    const dSku = `D${discId}`;

    console.log(`[taskQueueWorker.js] Setting Veeqo quantity to 0 for disc with id=${discId}, SKU=${dSku}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Get the Veeqo product IDs from the SKU
    const veeqoIds = await getVeeqoId(dSku);

    if (!veeqoIds || veeqoIds.length === 0) {
      console.log(`[taskQueueWorker.js] No Veeqo product found for SKU ${dSku}`);
      await updateTaskStatus(task.id, 'completed', {
        message: `dsku is not on veeqo`,
        disc_id: discId,
        sku: dSku
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Found ${veeqoIds.length} Veeqo products for SKU ${dSku}`);

    // Set the quantity to 0 for all Veeqo products with this SKU
    let successCount = 0;
    let failureCount = 0;
    let errors = [];

    for (const veeqoId of veeqoIds) {
      try {
        // Construct the API endpoint URL
        const veeqoApiKey = process.env.VEEQO_API_KEY;
        const url = `https://api.veeqo.com/sellables/${veeqoId}/warehouses/99881/stock_entry`;

        console.log(`[taskQueueWorker.js] Updating Veeqo quantity to 0 for product ID ${veeqoId}`);

        // Make the API request
        const response = await axios({
          method: 'PUT',
          url: url,
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': veeqoApiKey
          },
          data: {
            stock_entry: {
              physical_stock_level: 0,
              infinite: false
            }
          }
        });

        if (response.status === 200) {
          console.log(`[taskQueueWorker.js] Successfully updated Veeqo quantity to 0 for product ID ${veeqoId}`);
          successCount++;
        } else {
          const errMsg = `[taskQueueWorker.js] Error updating Veeqo quantity for product ID ${veeqoId}: ${response.status} ${response.statusText}`;
          console.error(errMsg);
          await logError(errMsg, `Updating Veeqo quantity for SKU ${dSku}`);
          failureCount++;
          errors.push({ veeqoId, error: `${response.status} ${response.statusText}` });
        }
      } catch (err) {
        const errMsg = `[taskQueueWorker.js] Exception while updating Veeqo quantity for product ID ${veeqoId}: ${err.message}`;
        console.error(errMsg);
        await logError(errMsg, `Updating Veeqo quantity for SKU ${dSku}`);
        failureCount++;
        errors.push({ veeqoId, error: err.message });
      }
    }

    // Update task status based on results
    if (failureCount === 0) {
      await updateTaskStatus(task.id, 'completed', {
        message: `Successfully updated Veeqo quantity to 0 for all ${successCount} variants of SKU ${dSku}`,
        disc_id: discId,
        sku: dSku,
        veeqo_ids: veeqoIds
      });
    } else if (successCount === 0) {
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update Veeqo quantity for any variants.",
        error: JSON.stringify(errors),
        disc_id: discId,
        sku: dSku,
        veeqo_ids: veeqoIds
      });
    } else {
      await updateTaskStatus(task.id, 'completed', {
        message: `Partially successful: Updated ${successCount} variants, failed to update ${failureCount} variants of SKU ${dSku}`,
        warnings: JSON.stringify(errors),
        disc_id: discId,
        sku: dSku,
        veeqo_ids: veeqoIds
      });
    }
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to update Veeqo quantity due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a disc_unsold_update_veeqo_qty_by_d_sku task
async function processDiscUnsoldUpdateVeeqoQtyByDSkuTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update Veeqo quantity. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update Veeqo quantity. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;
    const dSku = `D${discId}`;

    console.log(`[taskQueueWorker.js] Setting Veeqo quantity to 1 for disc with id=${discId}, SKU=${dSku}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Get the Veeqo product IDs from the SKU
    const veeqoIds = await getVeeqoId(dSku);

    if (!veeqoIds || veeqoIds.length === 0) {
      console.log(`[taskQueueWorker.js] No Veeqo product found for SKU ${dSku}`);
      await updateTaskStatus(task.id, 'completed', {
        message: `dsku is not on veeqo`,
        disc_id: discId,
        sku: dSku
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Found ${veeqoIds.length} Veeqo products for SKU ${dSku}`);

    // Set the quantity to 1 for all Veeqo products with this SKU
    let successCount = 0;
    let failureCount = 0;
    let errors = [];

    for (const veeqoId of veeqoIds) {
      try {
        // Construct the API endpoint URL
        const veeqoApiKey = process.env.VEEQO_API_KEY;
        const url = `https://api.veeqo.com/sellables/${veeqoId}/warehouses/99881/stock_entry`;

        console.log(`[taskQueueWorker.js] Updating Veeqo quantity to 1 for product ID ${veeqoId}`);

        // Make the API request
        const response = await axios({
          method: 'PUT',
          url: url,
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': veeqoApiKey
          },
          data: {
            stock_entry: {
              physical_stock_level: 1,
              infinite: false
            }
          }
        });

        if (response.status === 200) {
          console.log(`[taskQueueWorker.js] Successfully updated Veeqo quantity to 1 for product ID ${veeqoId}`);
          successCount++;
        } else {
          const errMsg = `[taskQueueWorker.js] Error updating Veeqo quantity for product ID ${veeqoId}: ${response.status} ${response.statusText}`;
          console.error(errMsg);
          await logError(errMsg, `Updating Veeqo quantity for SKU ${dSku}`);
          failureCount++;
          errors.push({ veeqoId, error: `${response.status} ${response.statusText}` });
        }
      } catch (err) {
        const errMsg = `[taskQueueWorker.js] Exception while updating Veeqo quantity for product ID ${veeqoId}: ${err.message}`;
        console.error(errMsg);
        await logError(errMsg, `Updating Veeqo quantity for SKU ${dSku}`);
        failureCount++;
        errors.push({ veeqoId, error: err.message });
      }
    }

    // Update task status based on results
    if (failureCount === 0) {
      await updateTaskStatus(task.id, 'completed', {
        message: `Successfully updated Veeqo quantity to 1 for all ${successCount} variants of SKU ${dSku}`,
        disc_id: discId,
        sku: dSku,
        veeqo_ids: veeqoIds
      });
    } else if (successCount === 0) {
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update Veeqo quantity for any variants.",
        error: JSON.stringify(errors),
        disc_id: discId,
        sku: dSku,
        veeqo_ids: veeqoIds
      });
    } else {
      await updateTaskStatus(task.id, 'completed', {
        message: `Partially successful: Updated ${successCount} variants, failed to update ${failureCount} variants of SKU ${dSku}`,
        warnings: JSON.stringify(errors),
        disc_id: discId,
        sku: dSku,
        veeqo_ids: veeqoIds
      });
    }
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to update Veeqo quantity due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a check_if_osl_is_ready task
async function processCheckOslIsReadyTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        // Parse JSON string
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload format: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if OSL is ready. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    const oslId = payload.id;

    console.log(`[taskQueueWorker.js] Checking if OSL with id=${oslId} is ready`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Retrieve the OSL record to check all required fields
    console.log(`[taskQueueWorker.js] Retrieving OSL record for id=${oslId}...`);
    const { data: oslRecord, error: fetchError } = await supabase
      .from('t_order_sheet_lines')
      .select('*, t_mps!inner(shopify_collection_uploaded_at, stamp_id, release_date_online, cost_price_reviewed_at, plastic_id, todo, t_plastics!inner(price_cost_verified_at))')
      .eq('id', oslId)
      .maybeSingle();

    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error retrieving OSL record: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Retrieving OSL record for id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if OSL is ready. Database error when retrieving OSL record.",
        error: fetchError.message
      });
      return;
    }

    if (!oslRecord) {
      const errMsg = `[taskQueueWorker.js] OSL record not found for id=${oslId}`;
      console.error(errMsg);
      await logError(errMsg, `Retrieving OSL record for id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if OSL is ready. OSL record not found.",
        error: 'Record not found'
      });
      return;
    }

    // Check required fields
    const requiredFields = ['mps_id', 'min_weight', 'max_weight', 'color_id'];
    const missingFields = [];

    for (const field of requiredFields) {
      if (oslRecord[field] === null || oslRecord[field] === undefined) {
        missingFields.push(field);
      }
    }

    // Check additional conditions
    const readyButtonIsTrue = oslRecord.ready_button === true;
    const shopifyUploadedAtIsNull = oslRecord.shopify_uploaded_at === null;

    // Check if the related MPS has shopify_collection_uploaded_at
    let mpsShopifyCollectionUploadedAtIsNotNull = false;
    if (oslRecord.mps_id !== null && oslRecord.t_mps) {
      mpsShopifyCollectionUploadedAtIsNotNull = oslRecord.t_mps.shopify_collection_uploaded_at !== null;
      console.log(`[taskQueueWorker.js] MPS ${oslRecord.mps_id} shopify_collection_uploaded_at: ${oslRecord.t_mps.shopify_collection_uploaded_at}`);
    }

    // Check price verification based on stamp_id
    let priceVerificationPassed = false;
    let priceVerificationReason = null;

    if (oslRecord.mps_id !== null && oslRecord.t_mps) {
      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);
      const ninetyDaysAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);

      const mpsRecord = oslRecord.t_mps;
      const stampId = mpsRecord.stamp_id;
      const releaseDate = mpsRecord.release_date_online ? new Date(mpsRecord.release_date_online) : null;
      const costPriceReviewedAt = mpsRecord.cost_price_reviewed_at ? new Date(mpsRecord.cost_price_reviewed_at) : null;

      console.log(`[taskQueueWorker.js] MPS ${oslRecord.mps_id} stamp_id: ${stampId}`);
      console.log(`[taskQueueWorker.js] MPS ${oslRecord.mps_id} release_date_online: ${releaseDate}`);
      console.log(`[taskQueueWorker.js] MPS ${oslRecord.mps_id} cost_price_reviewed_at: ${costPriceReviewedAt}`);

      if (stampId === 38) {
        // For stamp_id = 38, check t_plastics.price_cost_verified_at
        if (mpsRecord.plastic_id && mpsRecord.t_plastics) {
          const plasticPriceVerifiedAt = mpsRecord.t_plastics.price_cost_verified_at ? new Date(mpsRecord.t_plastics.price_cost_verified_at) : null;
          console.log(`[taskQueueWorker.js] Plastic ${mpsRecord.plastic_id} price_cost_verified_at: ${plasticPriceVerifiedAt}`);

          if (plasticPriceVerifiedAt && plasticPriceVerifiedAt > ninetyDaysAgo) {
            priceVerificationPassed = true;
          } else {
            priceVerificationReason = "Plastic price verification is missing or older than 90 days";
          }
        } else {
          priceVerificationReason = "Plastic record not found or plastic_id is null";
        }
      } else {
        // For other stamp_ids, check t_mps.cost_price_reviewed_at based on release_date_online
        if (releaseDate) {
          // If release date is in the future or within the last 30 days
          const isRecentOrFuture = releaseDate > thirtyDaysAgo || releaseDate > now;

          if (isRecentOrFuture) {
            if (costPriceReviewedAt && costPriceReviewedAt > thirtyDaysAgo) {
              priceVerificationPassed = true;
            } else {
              priceVerificationReason = "MPS cost price review is missing or older than 30 days (required for recent or future releases)";
            }
          } else {
            // For older releases, 60 days is acceptable
            if (costPriceReviewedAt && costPriceReviewedAt > sixtyDaysAgo) {
              priceVerificationPassed = true;
            } else {
              priceVerificationReason = "MPS cost price review is missing or older than 60 days";
            }
          }
        } else {
          // If release date is null, require 60 days
          if (costPriceReviewedAt && costPriceReviewedAt > sixtyDaysAgo) {
            priceVerificationPassed = true;
          } else {
            priceVerificationReason = "MPS cost price review is missing or older than 60 days (required when release date is not set)";
          }
        }
      }
    } else {
      priceVerificationReason = "MPS record not found or mps_id is null";
    }

    console.log(`[taskQueueWorker.js] Price verification passed: ${priceVerificationPassed}`);
    if (priceVerificationReason) {
      console.log(`[taskQueueWorker.js] Price verification reason: ${priceVerificationReason}`);
    }

    // Determine if the OSL is ready
    const isReady = missingFields.length === 0 && readyButtonIsTrue && shopifyUploadedAtIsNull &&
                   mpsShopifyCollectionUploadedAtIsNotNull && priceVerificationPassed;

    console.log(`[taskQueueWorker.js] OSL ${oslId} readiness check:`);
    console.log(`[taskQueueWorker.js] - Missing fields: ${missingFields.length > 0 ? missingFields.join(', ') : 'None'}`);
    console.log(`[taskQueueWorker.js] - ready_button: ${readyButtonIsTrue}`);
    console.log(`[taskQueueWorker.js] - shopify_uploaded_at is null: ${shopifyUploadedAtIsNull}`);
    console.log(`[taskQueueWorker.js] - MPS shopify_collection_uploaded_at is not null: ${mpsShopifyCollectionUploadedAtIsNotNull}`);
    console.log(`[taskQueueWorker.js] - Price verification passed: ${priceVerificationPassed}`);
    console.log(`[taskQueueWorker.js] - Overall ready status: ${isReady}`);

    // Prepare the update data
    const updateData = {
      ready: isReady
    };

    // Set the todo field based on ready status
    if (isReady) {
      // If the OSL is ready, set todo to 'ready' with a date stamp
      const now = new Date();
      updateData.todo = `ready ${now.toISOString()}`;
    } else {
      // If the OSL is not ready, update the todo field with the reasons
      let reasons = [];
      if (missingFields.length > 0) {
        reasons.push(`Missing fields: ${missingFields.join(', ')}`);
      }
      if (!readyButtonIsTrue) {
        reasons.push('ready_button is not TRUE');
      }
      if (!shopifyUploadedAtIsNull) {
        reasons.push('shopify_uploaded_at is not NULL');
      }
      if (!mpsShopifyCollectionUploadedAtIsNotNull) {
        // Get the MPS record and its todo field
        let mpsReason = 'MPS shopify_collection_uploaded_at is NULL';

        // If the MPS has a todo field, include that information
        if (oslRecord.t_mps && oslRecord.t_mps.todo) {
          mpsReason += ` (MPS todo: ${oslRecord.t_mps.todo})`;
        }

        reasons.push(mpsReason);
      }
      if (!priceVerificationPassed && priceVerificationReason) {
        reasons.push(priceVerificationReason);
      }
      updateData.todo = `OSL is not ready. Reasons: ${reasons.join('; ')}`;
    }

    // Update the OSL record
    const { error: updateError } = await supabase
      .from('t_order_sheet_lines')
      .update(updateData)
      .eq('id', oslId);

    if (updateError) {
      const errMsg = `[taskQueueWorker.js] Error updating OSL record: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating OSL record for id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if OSL is ready. Database error when updating OSL record.",
        error: updateError.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Successfully updated OSL record for id=${oslId} with ready=${isReady}`);

    // If OSL is ready, handle the publish workflow
    if (isReady) {
      try {
        // Check if ready field was already true before our update
        const wasAlreadyReady = oslRecord.ready === true;

        if (wasAlreadyReady) {
          // If it was already ready, we need to manually enqueue the publish task
          // since the trigger won't fire (no change from false to true)
          console.log(`[taskQueueWorker.js] OSL ${oslId} was already ready, manually enqueueing publish_product_osl task...`);

          // Get the release_date_online from the related t_mps record (same logic as trigger)
          const { data: mpsData, error: mpsError } = await supabase
            .from('t_mps')
            .select('release_date_online')
            .eq('id', oslRecord.mps_id)
            .single();

          if (mpsError) {
            console.error(`[taskQueueWorker.js] Error fetching MPS release date for OSL ${oslId}:`, mpsError);
            await logError(`Error fetching MPS release date for OSL ${oslId}: ${mpsError.message}`, `Processing task ${task.id}`);
          } else {
            // Calculate the scheduled time (same logic as trigger)
            let scheduledTime;
            if (mpsData.release_date_online) {
              // Schedule for 2 minutes before the release date
              scheduledTime = new Date(mpsData.release_date_online);
              scheduledTime.setMinutes(scheduledTime.getMinutes() - 2);

              // If the calculated time is in the past, use NOW() instead
              if (scheduledTime < new Date()) {
                scheduledTime = new Date();
              }
            } else {
              // If no release date, schedule for now
              scheduledTime = new Date();
            }

            // Insert the publish_product_osl task
            const { error: enqueueError } = await supabase
              .from('t_task_queue')
              .insert({
                task_type: 'publish_product_osl',
                payload: { id: oslId },
                status: 'pending',
                scheduled_at: scheduledTime.toISOString(),
                created_at: new Date().toISOString(),
                enqueued_by: 'check_if_osl_is_ready'
              });

            if (enqueueError) {
              console.error(`[taskQueueWorker.js] Error enqueueing publish_product_osl task for OSL ${oslId}:`, enqueueError);
              await logError(`Error enqueueing publish_product_osl task for OSL ${oslId}: ${enqueueError.message}`, `Processing task ${task.id}`);
            } else {
              console.log(`[taskQueueWorker.js] Successfully enqueued publish_product_osl task for OSL ${oslId} scheduled at ${scheduledTime.toISOString()}`);
            }
          }
        } else {
          // If it wasn't already ready, the database trigger will handle enqueueing the task
          console.log(`[taskQueueWorker.js] OSL ${oslId} ready field changed from false to true, trigger will enqueue publish_product_osl task`);
        }
      } catch (err) {
        console.error(`[taskQueueWorker.js] Exception while handling publish workflow for OSL ${oslId}:`, err);
        await logError(`Exception while handling publish workflow for OSL ${oslId}: ${err.message}`, `Processing task ${task.id}`);
      }
    } else {
      // If OSL is not ready, enqueue check_if_disc_is_ready tasks for all related discs
      // so their todo info gets updated with the OSL not ready information
      try {
        console.log(`[taskQueueWorker.js] OSL ${oslId} is not ready, enqueueing check_if_disc_is_ready tasks for related discs...`);

        // Find all discs that are linked to this OSL
        const { data: relatedDiscs, error: discsError } = await supabase
          .from('t_discs')
          .select('id')
          .eq('order_sheet_line_id', oslId);

        if (discsError) {
          console.error(`[taskQueueWorker.js] Error fetching related discs for OSL ${oslId}:`, discsError);
          await logError(`Error fetching related discs for OSL ${oslId}: ${discsError.message}`, `Processing task ${task.id}`);
        } else if (relatedDiscs && relatedDiscs.length > 0) {
          console.log(`[taskQueueWorker.js] Found ${relatedDiscs.length} discs related to OSL ${oslId}`);

          // Enqueue check_if_disc_is_ready task for each related disc
          let enqueuedCount = 0;
          let errorCount = 0;

          for (const disc of relatedDiscs) {
            try {
              const { error: enqueueError } = await supabase
                .from('t_task_queue')
                .insert({
                  task_type: 'check_if_disc_is_ready',
                  payload: { id: disc.id },
                  status: 'pending',
                  scheduled_at: new Date().toISOString(),
                  created_at: new Date().toISOString(),
                  enqueued_by: 'check_if_osl_is_ready'
                });

              if (enqueueError) {
                console.error(`[taskQueueWorker.js] Error enqueueing check_if_disc_is_ready task for disc ${disc.id}:`, enqueueError);
                await logError(`Error enqueueing check_if_disc_is_ready task for disc ${disc.id}: ${enqueueError.message}`, `Processing task ${task.id}`);
                errorCount++;
              } else {
                enqueuedCount++;
              }
            } catch (err) {
              console.error(`[taskQueueWorker.js] Exception while enqueueing task for disc ${disc.id}:`, err);
              await logError(`Exception while enqueueing task for disc ${disc.id}: ${err.message}`, `Processing task ${task.id}`);
              errorCount++;
            }
          }

          console.log(`[taskQueueWorker.js] Enqueued check_if_disc_is_ready tasks for ${enqueuedCount} discs related to OSL ${oslId}. Errors: ${errorCount}`);
        } else {
          console.log(`[taskQueueWorker.js] No discs found related to OSL ${oslId}`);
        }
      } catch (err) {
        console.error(`[taskQueueWorker.js] Exception while handling related discs for OSL ${oslId}:`, err);
        await logError(`Exception while handling related discs for OSL ${oslId}: ${err.message}`, `Processing task ${task.id}`);
      }
    }

    // Prepare the result message
    let resultMessage;
    if (isReady) {
      resultMessage = "Success! OSL is ready. All required fields are present and conditions are met. Ready field set to TRUE.";
    } else {
      // Use the same message that was set in the todo field, and mention disc checking
      resultMessage = `${updateData.todo} Related disc ready checks have been enqueued to update their todo information.`;
    }

    // Mark the task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: resultMessage,
      success: true,
      ready: isReady,
      missing_fields: missingFields,
      ready_button: readyButtonIsTrue,
      shopify_uploaded_at_is_null: shopifyUploadedAtIsNull,
      mps_shopify_collection_uploaded_at_is_not_null: mpsShopifyCollectionUploadedAtIsNotNull,
      price_verification_passed: priceVerificationPassed,
      price_verification_reason: priceVerificationReason
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to check if OSL is ready due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process an osl_uploaded_check_if_related_discs_are_ready task
async function processOslUploadedCheckRelatedDiscsTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        // Parse JSON string
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload format: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check related discs. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    const oslId = payload.id;

    console.log(`[taskQueueWorker.js] Checking related discs for OSL with id=${oslId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Find all discs related to this OSL
    console.log(`[taskQueueWorker.js] Finding discs with order_sheet_line_id=${oslId}...`);
    const { data: relatedDiscs, error: fetchError } = await supabase
      .from('t_discs')
      .select('id')
      .eq('order_sheet_line_id', oslId);

    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error retrieving related discs: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Retrieving discs for OSL id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check related discs. Database error when retrieving disc records.",
        error: fetchError.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Found ${relatedDiscs ? relatedDiscs.length : 0} discs related to OSL ${oslId}`);

    // If no related discs, mark task as completed
    if (!relatedDiscs || relatedDiscs.length === 0) {
      console.log(`[taskQueueWorker.js] No discs found for OSL ${oslId}`);
      await updateTaskStatus(task.id, 'completed', {
        message: "No related discs found for this OSL.",
        success: true,
        osl_id: oslId,
        discs_found: 0
      });
      return;
    }

    // First enqueue a verify_disc_image task, then a check_if_disc_is_ready task (delayed by 15s) for each related disc
    let enqueuedCount = 0;
    let errorCount = 0;

    for (const disc of relatedDiscs) {
      try {
        // Step 1: Enqueue verify_disc_image task
        console.log(`[taskQueueWorker.js] Enqueueing verify_disc_image task for disc id=${disc.id}`);

        const { error: verifyTaskError } = await supabase
          .from('t_task_queue')
          .insert({
            task_type: 'verify_disc_image',
            payload: { id: disc.id },
            status: 'pending',
            scheduled_at: new Date().toISOString(),
            created_at: new Date().toISOString()
          });

        if (verifyTaskError) {
          const errMsg = `[taskQueueWorker.js] Error creating verify_disc_image task for disc ${disc.id}: ${verifyTaskError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Creating verify task for disc id=${disc.id}`);
          errorCount++;
        } else {
          // Step 2: Enqueue check_if_disc_is_ready task with 30 second delay
          const delayedTime = new Date(new Date().getTime() + 30000); // 30 seconds later
          console.log(`[taskQueueWorker.js] Enqueueing check_if_disc_is_ready task for disc id=${disc.id} with 30s delay`);

          const { error: createTaskError } = await supabase
            .from('t_task_queue')
            .insert({
              task_type: 'check_if_disc_is_ready',
              payload: { id: disc.id },
              status: 'pending',
              scheduled_at: delayedTime.toISOString(),
              created_at: new Date().toISOString()
            });

          if (createTaskError) {
            const errMsg = `[taskQueueWorker.js] Error creating check_if_disc_is_ready task for disc ${disc.id}: ${createTaskError.message}`;
            console.error(errMsg);
            await logError(errMsg, `Creating check task for disc id=${disc.id}`);
            errorCount++;
          } else {
            enqueuedCount++;
          }
        }
      } catch (err) {
        const errMsg = `[taskQueueWorker.js] Exception while creating tasks for disc ${disc.id}: ${err.message}`;
        console.error(errMsg);
        await logError(errMsg, `Creating tasks for disc id=${disc.id}`);
        errorCount++;
      }
    }

    // Mark the task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully enqueued verify_disc_image and check_if_disc_is_ready tasks (with 30s delay) for ${enqueuedCount} discs. Errors: ${errorCount}`,
      success: true,
      osl_id: oslId,
      discs_found: relatedDiscs.length,
      tasks_enqueued: enqueuedCount * 2, // Two tasks per disc (verify and check)
      errors: errorCount
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to check related discs due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a mps_published_check_related_osls_for_ready task
async function processMpsPublishedCheckRelatedOslsTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        // Parse JSON string
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload format: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check related OSLs. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    const mpsId = payload.id;

    console.log(`[taskQueueWorker.js] Checking related OSLs for MPS with id=${mpsId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Find all order sheet lines related to this MPS
    console.log(`[taskQueueWorker.js] Finding OSLs with mps_id=${mpsId}...`);
    const { data: relatedOsls, error: fetchError } = await supabase
      .from('t_order_sheet_lines')
      .select('id')
      .eq('mps_id', mpsId);

    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error retrieving related OSLs: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Retrieving OSLs for MPS id=${mpsId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check related OSLs. Database error when retrieving OSL records.",
        error: fetchError.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Found ${relatedOsls ? relatedOsls.length : 0} OSLs related to MPS ${mpsId}`);

    // If no related OSLs, mark task as completed
    if (!relatedOsls || relatedOsls.length === 0) {
      console.log(`[taskQueueWorker.js] No OSLs found for MPS ${mpsId}`);
      await updateTaskStatus(task.id, 'completed', {
        message: "No related OSLs found for this MPS.",
        success: true,
        mps_id: mpsId,
        osls_found: 0
      });
      return;
    }

    // Enqueue a check_if_osl_is_ready task for each related OSL
    // with scheduled_at spaced 1 minute apart
    let enqueuedCount = 0;
    let errorCount = 0;
    const now = new Date();

    for (let i = 0; i < relatedOsls.length; i++) {
      const osl = relatedOsls[i];
      try {
        // Calculate scheduled time: now + (i * 1 minute)
        const scheduledTime = new Date(now.getTime() + (i * 60000));
        console.log(`[taskQueueWorker.js] Enqueueing check_if_osl_is_ready task for OSL id=${osl.id}, scheduled at ${scheduledTime.toISOString()}`);

        const { error: createTaskError } = await supabase
          .from('t_task_queue')
          .insert({
            task_type: 'check_if_osl_is_ready',
            payload: { id: osl.id },
            status: 'pending',
            scheduled_at: scheduledTime.toISOString(), // Schedule with 1 minute spacing
            created_at: new Date().toISOString(),
            enqueued_by: task.task_type
          });

        if (createTaskError) {
          const errMsg = `[taskQueueWorker.js] Error creating check_if_osl_is_ready task for OSL ${osl.id}: ${createTaskError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Creating task for OSL id=${osl.id}`);
          errorCount++;
        } else {
          enqueuedCount++;
        }
      } catch (err) {
        const errMsg = `[taskQueueWorker.js] Exception while creating task for OSL ${osl.id}: ${err.message}`;
        console.error(errMsg);
        await logError(errMsg, `Creating task for OSL id=${osl.id}`);
        errorCount++;
      }
    }

    // Mark the task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully enqueued check_if_osl_is_ready tasks for ${enqueuedCount} OSLs, spaced 1 minute apart. Errors: ${errorCount}`,
      success: true,
      mps_id: mpsId,
      osls_found: relatedOsls.length,
      tasks_enqueued: enqueuedCount,
      errors: errorCount
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to check related OSLs due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a check_if_plastic_is_ready task
async function processCheckIfPlasticIsReadyTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        // Parse JSON string
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload format: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if plastic is ready. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    const plasticId = payload.id;

    console.log(`[taskQueueWorker.js] Checking if plastic with id=${plasticId} is ready`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Get the plastic record with its related brand
    const { data: plasticRecord, error: plasticError } = await supabase
      .from('t_plastics')
      .select('*, t_brands!inner(shopify_collection_created_at)')
      .eq('id', plasticId)
      .maybeSingle();

    if (plasticError) {
      const errMsg = `[taskQueueWorker.js] Error fetching plastic record: ${plasticError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching plastic record for id=${plasticId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if plastic is ready. Database error when retrieving plastic record.",
        error: plasticError.message
      });
      return;
    }

    if (!plasticRecord) {
      const errMsg = `[taskQueueWorker.js] Plastic record not found for id=${plasticId}`;
      console.error(errMsg);
      await logError(errMsg, `Plastic record not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if plastic is ready. Plastic record not found.",
        error: 'Plastic record not found'
      });
      return;
    }

    // Check all the readiness rules
    const reasons = [];

    // Check if plastic is already published
    if (plasticRecord.shopify_collection_uploaded_at !== null) {
      console.log(`[taskQueueWorker.js] Plastic ${plasticId} already has a Shopify collection uploaded at ${plasticRecord.shopify_collection_uploaded_at}`);
      await updateTaskStatus(task.id, 'completed', {
        message: "Plastic already has a Shopify collection uploaded.",
        plastic_id: plasticId,
        shopify_collection_uploaded_at: plasticRecord.shopify_collection_uploaded_at
      });
      return;
    }

    // Check required fields
    if (!plasticRecord.plastic || plasticRecord.plastic.trim() === '') {
      reasons.push("plastic (name) is empty");
    }

    if (!plasticRecord.description || plasticRecord.description.trim() === '') {
      reasons.push("description is empty");
    }

    // Check code - must exist and match the pattern ^[A-Z0-9]+$
    if (!plasticRecord.code || plasticRecord.code.trim() === '') {
      reasons.push("code is empty");
    } else if (!/^[A-Z0-9]+$/.test(plasticRecord.code)) {
      reasons.push("code must match pattern ^[A-Z0-9]+$ (uppercase letters or digits only)");
    }

    if (plasticRecord.brand_id === null) {
      reasons.push("brand_id is null");
    }

    // Check ready_button
    if (!plasticRecord.ready_button) {
      reasons.push("ready_button is not true");
    }

    // Check brand's Shopify collection
    if (!plasticRecord.t_brands?.shopify_collection_created_at) {
      reasons.push("Brand's Shopify collection not created");
    }

    // Log the readiness check results
    console.log(`[taskQueueWorker.js] Plastic ${plasticId} readiness check:`);
    console.log(`[taskQueueWorker.js] - plastic: ${plasticRecord.plastic || 'null'}`);
    console.log(`[taskQueueWorker.js] - description: ${plasticRecord.description || 'null'}`);
    console.log(`[taskQueueWorker.js] - code: ${plasticRecord.code || 'null'}`);
    console.log(`[taskQueueWorker.js] - code format valid: ${plasticRecord.code && /^[A-Z0-9]+$/.test(plasticRecord.code) ? 'yes' : 'no'}`);
    console.log(`[taskQueueWorker.js] - brand_id: ${plasticRecord.brand_id || 'null'}`);
    console.log(`[taskQueueWorker.js] - ready_button: ${plasticRecord.ready_button}`);
    console.log(`[taskQueueWorker.js] - brand's shopify_collection_created_at: ${plasticRecord.t_brands?.shopify_collection_created_at || 'null'}`);
    console.log(`[taskQueueWorker.js] - reasons: ${reasons.length > 0 ? reasons.join(', ') : 'none'}`);

    // Determine if plastic is ready
    const isReady = reasons.length === 0;

    // Update the plastic record
    const todoMessage = isReady ? null : reasons.join(', ');
    const { error: updateError } = await supabase
      .from('t_plastics')
      .update({
        ready: isReady,
        todo: todoMessage
      })
      .eq('id', plasticId);

    if (updateError) {
      const errMsg = `[taskQueueWorker.js] Error updating plastic record: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating plastic record for id=${plasticId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update plastic readiness status.",
        error: updateError.message
      });
      return;
    }

    // Mark the task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: isReady ? "Plastic is ready." : "Plastic is not ready.",
      plastic_id: plasticId,
      is_ready: isReady,
      reasons: reasons.length > 0 ? reasons : null
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to check if plastic is ready due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a publish_plastic_collection task
async function processPublishPlasticCollectionTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        // Parse JSON string
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload format: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to publish plastic collection. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    const plasticId = payload.id;

    console.log(`[taskQueueWorker.js] Publishing collection for plastic with id=${plasticId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Check Shopify API credentials
    const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
    const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;
    if (!shopifyEndpoint || !shopifyAccessToken) {
      const errMsg = `[taskQueueWorker.js] Missing Shopify endpoint or access token in environment variables`;
      console.error(errMsg);
      await logError(errMsg, `Shopify API credentials missing`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to publish plastic collection. Shopify API credentials missing.",
        error: 'Missing Shopify API credentials'
      });
      return;
    }
    const smartCollectionsEndpoint = shopifyEndpoint.replace('graphql.json', 'smart_collections.json');
    console.log(`[taskQueueWorker.js] Shopify smart collections endpoint: ${smartCollectionsEndpoint}`);

    // We know the plastic is ready to be published because this task is triggered by the ready field changing to true
    console.log(`[taskQueueWorker.js] Plastic ${plasticId} is ready to be published, proceeding with Shopify collection creation...`);

    // Get the plastic record with brand information
    const { data: plasticRecord, error: plasticError } = await supabase
      .from('t_plastics')
      .select('*, t_brands!inner(id, brand, shopify_collection_created_at)')
      .eq('id', plasticId)
      .maybeSingle();

    if (plasticError) {
      const errMsg = `[taskQueueWorker.js] Error fetching plastic record: ${plasticError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching plastic record for id=${plasticId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to publish plastic collection. Database error when retrieving plastic record.",
        error: plasticError.message
      });
      return;
    }

    if (!plasticRecord) {
      const errMsg = `[taskQueueWorker.js] Plastic record not found for id=${plasticId}`;
      console.error(errMsg);
      await logError(errMsg, `Plastic record not found`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to publish plastic collection. Plastic record not found.",
        error: 'Plastic record not found'
      });
      return;
    }

    // Check if plastic is already published
    if (plasticRecord.shopify_collection_uploaded_at !== null) {
      const noteMessage = 'Plastic is not eligible for publishing as it already has a Shopify collection.';
      console.log(`[taskQueueWorker.js] ${noteMessage} Uploaded at: ${plasticRecord.shopify_collection_uploaded_at}`);

      // Update the notes field
      const { error: noteError } = await supabase
        .from('t_plastics')
        .update({ shopify_collection_uploaded_notes: noteMessage })
        .eq('id', plasticId);

      if (noteError) {
        console.error(`[taskQueueWorker.js] Failed to update shopify_collection_uploaded_notes: ${noteError.message}`);
      }

      await updateTaskStatus(task.id, 'completed', {
        message: noteMessage,
        plastic_id: plasticId,
        shopify_collection_uploaded_at: plasticRecord.shopify_collection_uploaded_at
      });
      return;
    }

    // Prepare the plastic record for creating the Shopify collection
    const brandName = plasticRecord.t_brands?.brand;
    if (!brandName) {
      const errMsg = `[taskQueueWorker.js] Brand name not found for plastic id ${plasticId}`;
      console.error(errMsg);
      await logError(errMsg, `Brand name missing`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to publish plastic collection. Brand name not found.",
        error: 'Brand name missing'
      });
      return;
    }

    // Create the Shopify smart collection
    try {
      console.log(`[taskQueueWorker.js] Creating Shopify smart collection for plastic ${plasticRecord.plastic}...`);

      // Generate the handle for the collection
      const handle = generatePlasticHandle(brandName, plasticRecord.plastic);
      console.log(`[taskQueueWorker.js] Generated handle: ${handle}`);

      // Prepare the payload for the Shopify API
      const payload = {
        smart_collection: {
          title: brandName + " " + plasticRecord.plastic + " Plastic",
          body_html: plasticRecord.description,
          handle: handle,
          sort_order: "best-selling",
          template_suffix: "plastic-collection",
          published: true,
          published_scope: "global",
          disjunctive: false,
          rules: [
            {
              column: "tag",
              relation: "equals",
              condition: "disc_plastic_" + plasticRecord.plastic
            },
            {
              column: "variant_inventory",
              relation: "greater_than",
              condition: "0"
            }
          ]
        }
      };

      // Make the API call to Shopify
      const response = await fetch(smartCollectionsEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Shopify-Access-Token': shopifyAccessToken
        },
        body: JSON.stringify(payload)
      });

      const result = await response.json();
      if (!response.ok) {
        throw new Error(
          `Error creating smart collection for plastic "${plasticRecord.plastic}": ${JSON.stringify(result)}`
        );
      }

      const collection = result.smart_collection;
      console.log(`[taskQueueWorker.js] Successfully created collection for plastic ${plasticRecord.plastic}:`);
      console.log(collection);

      // Update t_plastics with success information
      const successNote = "Success! Plastic Collection created on Shopify through task queue worker > Shopify API.";
      const { error: updateError } = await supabase
        .from('t_plastics')
        .update({
          shopify_collection_uploaded_at: new Date().toISOString(),
          shopify_collection_uploaded_notes: successNote
        })
        .eq('id', plasticId);

      if (updateError) {
        const errMsg = `[taskQueueWorker.js] Failed to update shopify_collection_uploaded_at: ${updateError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Updating shopify_collection_uploaded_at`);
        await updateTaskStatus(task.id, 'error', {
          message: "Created Shopify collection but failed to update database record.",
          error: updateError.message,
          collection: collection
        });
        return;
      }

      // Mark the task as completed
      await updateTaskStatus(task.id, 'completed', {
        message: successNote,
        plastic_id: plasticId,
        plastic_name: plasticRecord.plastic,
        brand_name: brandName,
        collection_id: collection.id,
        collection_handle: collection.handle
      });
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Failed to create collection for plastic ${plasticRecord.plastic}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Creating Shopify collection`);

      // Update the notes field with the error
      const { error: noteError } = await supabase
        .from('t_plastics')
        .update({ shopify_collection_uploaded_notes: err.message })
        .eq('id', plasticId);

      if (noteError) {
        console.error(`[taskQueueWorker.js] Failed to update shopify_collection_uploaded_notes: ${noteError.message}`);
      }

      await updateTaskStatus(task.id, 'error', {
        message: "Failed to create Shopify collection.",
        error: err.message,
        plastic_id: plasticId,
        plastic_name: plasticRecord.plastic
      });
    }
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to publish plastic collection due to an unexpected error.",
      error: err.message
    });
  }
}

/**
 * Generates a handle for a plastic collection.
 * This function concatenates the brand and plastic values and applies a series of replacements.
 *
 * @param {string} brand - The brand name.
 * @param {string} plastic - The plastic name.
 * @returns {string} - The generated handle.
 */
function generatePlasticHandle(brand, plastic) {
  let base = (brand + " " + plastic).toLowerCase();
  base = base.replace(/\./g, "");      // Remove periods.
  base = base.replace(/ /g, "-");      // Replace spaces with dashes.
  base = base.replace(/'/g, "");       // Remove apostrophes.
  base = base.replace(/\//g, "");      // Remove forward slashes.
  base = base.replace(/&/g, "-");      // Replace ampersands with dashes.
  base = base.replace(/\(/g, "");      // Remove "(".
  base = base.replace(/\)/g, "");      // Remove ")".
  base = base.replace(/"/g, "");       // Remove double quotes.
  base = base.replace(/-#/g, "");      // Remove "-#".
  base = base.replace(/-\$/g, "");     // Remove "-$".
  while (base.includes("--")) {
    base = base.replace(/--/g, "-");   // Replace double dashes with a single dash.
  }
  return base + "-plastic";
}

// Function to process a mps_price_verified_try_upload_osls task
async function processMpsPriceVerifiedTryUploadOslsTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        // Parse JSON string
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload format: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process MPS price verified task. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    const mpsId = payload.id;

    console.log(`[taskQueueWorker.js] Finding OSLs for MPS with id=${mpsId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Find all order sheet lines related to this MPS
    console.log(`[taskQueueWorker.js] Finding OSLs with mps_id=${mpsId}...`);
    const { data: relatedOsls, error: fetchError } = await supabase
      .from('t_order_sheet_lines')
      .select('id')
      .eq('mps_id', mpsId);

    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error retrieving related OSLs: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Retrieving OSLs for MPS id=${mpsId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to find related OSLs. Database error.",
        error: fetchError.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Found ${relatedOsls ? relatedOsls.length : 0} OSLs related to MPS ${mpsId}`);

    // If no related OSLs, mark task as completed
    if (!relatedOsls || relatedOsls.length === 0) {
      console.log(`[taskQueueWorker.js] No OSLs found for MPS ${mpsId}`);
      await updateTaskStatus(task.id, 'completed', {
        message: "No related OSLs found for this MPS.",
        success: true,
        mps_id: mpsId,
        osls_found: 0
      });
      return;
    }

    // Enqueue a check_if_osl_is_ready task for each related OSL
    let enqueuedCount = 0;
    let errorCount = 0;

    for (const osl of relatedOsls) {
      try {
        console.log(`[taskQueueWorker.js] Enqueueing check_if_osl_is_ready task for OSL id=${osl.id}`);

        const { error: createTaskError } = await supabase
          .from('t_task_queue')
          .insert({
            task_type: 'check_if_osl_is_ready',
            payload: { id: osl.id },
            status: 'pending',
            scheduled_at: new Date().toISOString(), // Schedule immediately
            created_at: new Date().toISOString(),
            enqueued_by: task.task_type
          });

        if (createTaskError) {
          const errMsg = `[taskQueueWorker.js] Error creating check_if_osl_is_ready task for OSL ${osl.id}: ${createTaskError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Creating task for OSL id=${osl.id}`);
          errorCount++;
        } else {
          enqueuedCount++;
        }
      } catch (err) {
        const errMsg = `[taskQueueWorker.js] Exception while creating task for OSL ${osl.id}: ${err.message}`;
        console.error(errMsg);
        await logError(errMsg, `Creating task for OSL id=${osl.id}`);
        errorCount++;
      }
    }

    // Mark the task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully enqueued check_if_osl_is_ready tasks for ${enqueuedCount} OSLs. Errors: ${errorCount}`,
      success: true,
      mps_id: mpsId,
      osls_found: relatedOsls.length,
      tasks_enqueued: enqueuedCount,
      errors: errorCount
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process MPS price verified task due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a plastics_uploaded_check_related_mps_for_ready task
async function processPlasticsUploadedCheckRelatedMpsTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        // Parse JSON string
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload format: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process plastics uploaded check related MPS task. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    const plasticId = payload.id;

    console.log(`[taskQueueWorker.js] Finding MPS records for plastic with id=${plasticId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Find all MPS records with this plastic_id that haven't been uploaded to Shopify
    console.log(`[taskQueueWorker.js] Finding MPS records with plastic_id=${plasticId} and shopify_collection_uploaded_at is null...`);
    const { data: relatedMps, error: fetchError } = await supabase
      .from('t_mps')
      .select('id')
      .eq('plastic_id', plasticId)
      .is('shopify_collection_uploaded_at', null);

    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error retrieving related MPS records: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Retrieving MPS records for plastic id=${plasticId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to find related MPS records. Database error.",
        error: fetchError.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Found ${relatedMps ? relatedMps.length : 0} MPS records related to plastic ${plasticId}`);

    // If no related MPS records, mark task as completed
    if (!relatedMps || relatedMps.length === 0) {
      console.log(`[taskQueueWorker.js] No MPS records found for plastic ${plasticId}`);
      await updateTaskStatus(task.id, 'completed', {
        message: "No related MPS records found for this plastic.",
        success: true,
        plastic_id: plasticId,
        mps_found: 0
      });
      return;
    }

    // Enqueue a check_if_mps_is_ready task for each related MPS
    let enqueuedCount = 0;
    let errorCount = 0;

    for (const mps of relatedMps) {
      try {
        console.log(`[taskQueueWorker.js] Enqueueing check_if_mps_is_ready task for MPS id=${mps.id}`);

        const { error: createTaskError } = await supabase
          .from('t_task_queue')
          .insert({
            task_type: 'check_if_mps_is_ready',
            payload: { id: mps.id },
            status: 'pending',
            scheduled_at: new Date().toISOString(), // Schedule immediately
            created_at: new Date().toISOString(),
            enqueued_by: task.task_type
          });

        if (createTaskError) {
          const errMsg = `[taskQueueWorker.js] Error creating check_if_mps_is_ready task for MPS ${mps.id}: ${createTaskError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Creating task for MPS id=${mps.id}`);
          errorCount++;
        } else {
          enqueuedCount++;
        }
      } catch (err) {
        const errMsg = `[taskQueueWorker.js] Exception while creating task for MPS ${mps.id}: ${err.message}`;
        console.error(errMsg);
        await logError(errMsg, `Creating task for MPS id=${mps.id}`);
        errorCount++;
      }
    }

    // Mark the task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully enqueued check_if_mps_is_ready tasks for ${enqueuedCount} MPS records. Errors: ${errorCount}`,
      success: true,
      plastic_id: plasticId,
      mps_found: relatedMps.length,
      tasks_enqueued: enqueuedCount,
      errors: errorCount
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process plastics uploaded check related MPS task due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a check_if_mps_is_ready task
async function processCheckIfMpsIsReadyTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        // Parse JSON string
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload format: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process check if MPS is ready task. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    const mpsId = payload.id;

    console.log(`[taskQueueWorker.js] Checking if MPS with id=${mpsId} is ready`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Retrieve the MPS record with related data
    console.log(`[taskQueueWorker.js] Retrieving MPS record for id=${mpsId}...`);
    const { data: mpsRecord, error: fetchError } = await supabase
      .from('t_mps')
      .select(`
        *,
        t_plastics(shopify_collection_uploaded_at),
        t_molds(shopify_collection_created_at)
      `)
      .eq('id', mpsId)
      .maybeSingle();

    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error retrieving MPS record: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Retrieving MPS record for id=${mpsId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if MPS is ready. Database error when retrieving MPS record.",
        error: fetchError.message
      });
      return;
    }

    if (!mpsRecord) {
      const errMsg = `[taskQueueWorker.js] No MPS record found for id=${mpsId}`;
      console.error(errMsg);
      await logError(errMsg, `No MPS record for id=${mpsId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if MPS is ready. MPS record not found.",
        error: 'MPS record not found'
      });
      return;
    }

    // Check if required fields are not null
    const reasons = [];

    if (mpsRecord.plastic_id === null) {
      reasons.push("plastic_id is null");
    }

    if (mpsRecord.mold_id === null) {
      reasons.push("mold_id is null");
    }

    if (mpsRecord.stamp_id === null) {
      reasons.push("stamp_id is null");
    }

    // Check if plastic has shopify_collection_uploaded_at
    if (mpsRecord.plastic_id !== null &&
        (!mpsRecord.t_plastics || mpsRecord.t_plastics.shopify_collection_uploaded_at === null)) {
      reasons.push("plastic's shopify_collection_uploaded_at is null");
    }

    // Check if mold has shopify_collection_created_at
    if (mpsRecord.mold_id !== null &&
        (!mpsRecord.t_molds || mpsRecord.t_molds.shopify_collection_created_at === null)) {
      reasons.push("mold's shopify_collection_created_at is null");
    }

    // Check if there's a verified image for this MPS
    const { data: imageRecords, error: imageError } = await supabase
      .from('t_images')
      .select('*')
      .eq('table_name', 't_mps')
      .eq('record_id', mpsId)
      .eq('image_verified', true);

    if (imageError) {
      const errMsg = `[taskQueueWorker.js] Error retrieving image records: ${imageError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Retrieving image records for MPS id=${mpsId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if MPS is ready. Database error when retrieving image records.",
        error: imageError.message
      });
      return;
    }

    if (!imageRecords || imageRecords.length === 0) {
      reasons.push("no verified image found");
    }

    // Determine if the MPS is ready
    const isReady = reasons.length === 0;

    console.log(`[taskQueueWorker.js] MPS ${mpsId} readiness check:`);
    console.log(`[taskQueueWorker.js] - plastic_id: ${mpsRecord.plastic_id !== null ? 'present' : 'missing'}`);
    console.log(`[taskQueueWorker.js] - mold_id: ${mpsRecord.mold_id !== null ? 'present' : 'missing'}`);
    console.log(`[taskQueueWorker.js] - stamp_id: ${mpsRecord.stamp_id !== null ? 'present' : 'missing'}`);

    if (mpsRecord.plastic_id !== null) {
      console.log(`[taskQueueWorker.js] - plastic's shopify_collection_uploaded_at: ${mpsRecord.t_plastics?.shopify_collection_uploaded_at || 'null'}`);
    }

    if (mpsRecord.mold_id !== null) {
      console.log(`[taskQueueWorker.js] - mold's shopify_collection_created_at: ${mpsRecord.t_molds?.shopify_collection_created_at || 'null'}`);
    }

    console.log(`[taskQueueWorker.js] - verified images: ${imageRecords ? imageRecords.length : 0}`);
    console.log(`[taskQueueWorker.js] - Overall ready status: ${isReady}`);

    // Update the MPS record
    const todoMessage = isReady ? null : reasons.join(', ');
    const { error: updateError } = await supabase
      .from('t_mps')
      .update({
        ready: isReady,
        todo: todoMessage
      })
      .eq('id', mpsId);

    if (updateError) {
      const errMsg = `[taskQueueWorker.js] Error updating MPS record: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating MPS record for id=${mpsId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update MPS ready status. Database error.",
        error: updateError.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Successfully updated MPS record for id=${mpsId} with ready=${isReady}`);

    // If MPS is not ready, enqueue check_if_osl_is_ready tasks for all related OSLs
    // so their todo info gets updated with the MPS not ready information
    if (!isReady) {
      try {
        console.log(`[taskQueueWorker.js] MPS ${mpsId} is not ready, enqueueing check_if_osl_is_ready tasks for related OSLs...`);

        // Find all OSLs that are linked to this MPS
        const { data: relatedOsls, error: oslsError } = await supabase
          .from('t_order_sheet_lines')
          .select('id')
          .eq('mps_id', mpsId);

        if (oslsError) {
          console.error(`[taskQueueWorker.js] Error fetching related OSLs for MPS ${mpsId}:`, oslsError);
          await logError(`Error fetching related OSLs for MPS ${mpsId}: ${oslsError.message}`, `Processing task ${task.id}`);
        } else if (relatedOsls && relatedOsls.length > 0) {
          console.log(`[taskQueueWorker.js] Found ${relatedOsls.length} OSLs related to MPS ${mpsId}`);

          // Enqueue check_if_osl_is_ready task for each related OSL
          let enqueuedCount = 0;
          let errorCount = 0;

          for (const osl of relatedOsls) {
            try {
              const { error: enqueueError } = await supabase
                .from('t_task_queue')
                .insert({
                  task_type: 'check_if_osl_is_ready',
                  payload: { id: osl.id },
                  status: 'pending',
                  scheduled_at: new Date().toISOString(),
                  created_at: new Date().toISOString(),
                  enqueued_by: 'check_if_mps_is_ready'
                });

              if (enqueueError) {
                console.error(`[taskQueueWorker.js] Error enqueueing check_if_osl_is_ready task for OSL ${osl.id}:`, enqueueError);
                await logError(`Error enqueueing check_if_osl_is_ready task for OSL ${osl.id}: ${enqueueError.message}`, `Processing task ${task.id}`);
                errorCount++;
              } else {
                enqueuedCount++;
              }
            } catch (err) {
              console.error(`[taskQueueWorker.js] Exception while enqueueing task for OSL ${osl.id}:`, err);
              await logError(`Exception while enqueueing task for OSL ${osl.id}: ${err.message}`, `Processing task ${task.id}`);
              errorCount++;
            }
          }

          console.log(`[taskQueueWorker.js] Enqueued check_if_osl_is_ready tasks for ${enqueuedCount} OSLs related to MPS ${mpsId}. Errors: ${errorCount}`);
        } else {
          console.log(`[taskQueueWorker.js] No OSLs found related to MPS ${mpsId}`);
        }
      } catch (err) {
        console.error(`[taskQueueWorker.js] Exception while handling related OSLs for MPS ${mpsId}:`, err);
        await logError(`Exception while handling related OSLs for MPS ${mpsId}: ${err.message}`, `Processing task ${task.id}`);
      }
    }

    // Prepare the result message
    let resultMessage;
    if (isReady) {
      resultMessage = "Success! MPS is ready. All required fields are present and conditions are met.";
    } else {
      resultMessage = `MPS is not ready. Reasons: ${reasons.join('; ')} Related OSL ready checks have been enqueued to update their todo information.`;
    }

    // Mark the task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: resultMessage,
      success: true,
      ready: isReady,
      reasons: reasons.length > 0 ? reasons : null
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to check if MPS is ready due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a mps_is_ready_so_publish task
async function processMpsIsReadySoPublishTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        // Parse JSON string
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload format: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process MPS publish task. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    const mpsId = payload.id;

    console.log(`[taskQueueWorker.js] Publishing MPS with id=${mpsId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Verify that the MPS is ready and hasn't been published yet
    console.log(`[taskQueueWorker.js] Retrieving MPS record for id=${mpsId}...`);
    const { data: mpsRecord, error: fetchError } = await supabase
      .from('t_mps')
      .select('*')
      .eq('id', mpsId)
      .maybeSingle();

    if (fetchError) {
      const errMsg = `[taskQueueWorker.js] Error retrieving MPS record: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Retrieving MPS record for id=${mpsId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to publish MPS. Database error when retrieving MPS record.",
        error: fetchError.message
      });
      return;
    }

    if (!mpsRecord) {
      const errMsg = `[taskQueueWorker.js] No MPS record found for id=${mpsId}`;
      console.error(errMsg);
      await logError(errMsg, `No MPS record for id=${mpsId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to publish MPS. MPS record not found.",
        error: 'MPS record not found'
      });
      return;
    }

    // Check if the MPS is ready
    if (!mpsRecord.ready) {
      const errMsg = `[taskQueueWorker.js] MPS id=${mpsId} is not ready for publishing`;
      console.error(errMsg);
      await logError(errMsg, `MPS not ready for id=${mpsId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to publish MPS. MPS is not ready for publishing.",
        error: 'MPS not ready'
      });
      return;
    }

    // Check if the MPS has already been published
    if (mpsRecord.shopify_collection_uploaded_at !== null) {
      console.log(`[taskQueueWorker.js] MPS id=${mpsId} has already been published at ${mpsRecord.shopify_collection_uploaded_at}`);
      await updateTaskStatus(task.id, 'completed', {
        message: "MPS has already been published to Shopify.",
        shopify_collection_uploaded_at: mpsRecord.shopify_collection_uploaded_at
      });
      return;
    }

    // Spawn the publishCollectionMPS.js process
    const publishCollectionMpsPath = path.join(__dirname, 'publishCollectionMPS.js');
    console.log(`[taskQueueWorker.js] Spawning process: node ${publishCollectionMpsPath} --id=${mpsId}`);

    const publishProcess = spawn('node', [publishCollectionMpsPath, `--id=${mpsId}`]);

    // Collect stdout and stderr
    let stdout = '';
    let stderr = '';

    publishProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      console.log(`[publishCollectionMPS.js] ${output.trim()}`);
    });

    publishProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      console.error(`[publishCollectionMPS.js] ${output.trim()}`);
    });

    // Handle process completion
    publishProcess.on('close', async (code) => {
      console.log(`[taskQueueWorker.js] publishCollectionMPS.js process exited with code ${code}`);

      if (code === 0) {
        // Success
        console.log(`[taskQueueWorker.js] Successfully published MPS with id=${mpsId}`);
        await updateTaskStatus(task.id, 'completed', {
          message: "Success! MPS published to Shopify.",
          mps_id: mpsId,
          stdout: stdout
        });
      } else {
        // Failure - check if this is a retry attempt
        const isRetry = payload.retry_attempt === true;

        if (!isRetry) {
          // First failure - schedule retry in 5 minutes
          console.log(`[taskQueueWorker.js] First failure for MPS ${mpsId}, scheduling retry in 5 minutes...`);

          try {
            // Calculate retry time (5 minutes from now)
            const retryTime = new Date();
            retryTime.setMinutes(retryTime.getMinutes() + 5);

            // Create retry task
            const { error: retryError } = await supabase
              .from('t_task_queue')
              .insert({
                task_type: 'mps_is_ready_so_publish',
                payload: { id: mpsId, retry_attempt: true },
                status: 'pending',
                scheduled_at: retryTime.toISOString(),
                created_at: new Date().toISOString(),
                enqueued_by: 'mps_is_ready_so_publish_retry'
              });

            if (retryError) {
              console.error(`[taskQueueWorker.js] Error creating retry task for MPS ${mpsId}:`, retryError);
              await logError(`Error creating retry task for MPS ${mpsId}: ${retryError.message}`, `Processing task ${task.id}`);

              // If we can't create retry, mark as error
              await updateTaskStatus(task.id, 'error', {
                message: "Failed to publish MPS to Shopify and failed to schedule retry.",
                error: stderr || `Process exited with code ${code}`,
                mps_id: mpsId,
                stdout: stdout
              });
            } else {
              console.log(`[taskQueueWorker.js] Successfully scheduled retry for MPS ${mpsId} at ${retryTime.toISOString()}`);

              // Mark original task as completed with retry info
              await updateTaskStatus(task.id, 'completed', {
                message: "First publish attempt failed. Retry scheduled for 5 minutes later.",
                mps_id: mpsId,
                retry_scheduled_at: retryTime.toISOString(),
                original_error: stderr || `Process exited with code ${code}`,
                stdout: stdout
              });
            }
          } catch (err) {
            console.error(`[taskQueueWorker.js] Exception while scheduling retry for MPS ${mpsId}:`, err);
            await logError(`Exception while scheduling retry for MPS ${mpsId}: ${err.message}`, `Processing task ${task.id}`);

            // If we can't schedule retry, mark as error
            await updateTaskStatus(task.id, 'error', {
              message: "Failed to publish MPS to Shopify and failed to schedule retry due to exception.",
              error: stderr || `Process exited with code ${code}`,
              mps_id: mpsId,
              stdout: stdout
            });
          }
        } else {
          // Second failure - mark as error permanently
          console.log(`[taskQueueWorker.js] Second failure for MPS ${mpsId}, marking as permanent error`);

          // Extract error message from stdout if possible
          let errorMessage = "Unknown error";
          const errorLines = stdout.split('\n').filter(line => line.includes('ERROR:'));
          if (errorLines.length > 0) {
            errorMessage = errorLines[errorLines.length - 1].replace('ERROR:', '').trim();
          }

          await updateTaskStatus(task.id, 'error', {
            message: "Failed to publish MPS to Shopify after retry attempt.",
            error: errorMessage || stderr || `Process exited with code ${code}`,
            mps_id: mpsId,
            retry_attempt: true,
            stdout: stdout
          });
        }
      }
    });

    // Handle process error
    publishProcess.on('error', async (err) => {
      const errMsg = `[taskQueueWorker.js] Error spawning publishCollectionMPS.js process: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);

      // Check if this is a retry attempt
      const isRetry = payload.retry_attempt === true;

      if (!isRetry) {
        // First failure - schedule retry in 5 minutes
        console.log(`[taskQueueWorker.js] First process error for MPS ${mpsId}, scheduling retry in 5 minutes...`);

        try {
          // Calculate retry time (5 minutes from now)
          const retryTime = new Date();
          retryTime.setMinutes(retryTime.getMinutes() + 5);

          // Create retry task
          const { error: retryError } = await supabase
            .from('t_task_queue')
            .insert({
              task_type: 'mps_is_ready_so_publish',
              payload: { id: mpsId, retry_attempt: true },
              status: 'pending',
              scheduled_at: retryTime.toISOString(),
              created_at: new Date().toISOString(),
              enqueued_by: 'mps_is_ready_so_publish_retry'
            });

          if (retryError) {
            console.error(`[taskQueueWorker.js] Error creating retry task for MPS ${mpsId}:`, retryError);
            await logError(`Error creating retry task for MPS ${mpsId}: ${retryError.message}`, `Processing task ${task.id}`);

            // If we can't create retry, mark as error
            await updateTaskStatus(task.id, 'error', {
              message: "Error spawning publishCollectionMPS.js process and failed to schedule retry.",
              error: err.message,
              mps_id: mpsId
            });
          } else {
            console.log(`[taskQueueWorker.js] Successfully scheduled retry for MPS ${mpsId} at ${retryTime.toISOString()}`);

            // Mark original task as completed with retry info
            await updateTaskStatus(task.id, 'completed', {
              message: "First publish attempt failed due to process error. Retry scheduled for 5 minutes later.",
              mps_id: mpsId,
              retry_scheduled_at: retryTime.toISOString(),
              original_error: err.message
            });
          }
        } catch (retryErr) {
          console.error(`[taskQueueWorker.js] Exception while scheduling retry for MPS ${mpsId}:`, retryErr);
          await logError(`Exception while scheduling retry for MPS ${mpsId}: ${retryErr.message}`, `Processing task ${task.id}`);

          // If we can't schedule retry, mark as error
          await updateTaskStatus(task.id, 'error', {
            message: "Error spawning publishCollectionMPS.js process and failed to schedule retry due to exception.",
            error: err.message,
            mps_id: mpsId
          });
        }
      } else {
        // Second failure - mark as error permanently
        console.log(`[taskQueueWorker.js] Second process error for MPS ${mpsId}, marking as permanent error`);

        await updateTaskStatus(task.id, 'error', {
          message: "Error spawning publishCollectionMPS.js process after retry attempt.",
          error: err.message,
          mps_id: mpsId,
          retry_attempt: true
        });
      }
    });
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    // Check if this is a retry attempt
    const isRetry = payload && payload.retry_attempt === true;

    if (!isRetry) {
      // First failure - schedule retry in 5 minutes
      console.log(`[taskQueueWorker.js] First exception for MPS ${payload?.id || 'unknown'}, scheduling retry in 5 minutes...`);

      try {
        // Calculate retry time (5 minutes from now)
        const retryTime = new Date();
        retryTime.setMinutes(retryTime.getMinutes() + 5);

        // Create retry task
        const { error: retryError } = await supabase
          .from('t_task_queue')
          .insert({
            task_type: 'mps_is_ready_so_publish',
            payload: { id: payload?.id, retry_attempt: true },
            status: 'pending',
            scheduled_at: retryTime.toISOString(),
            created_at: new Date().toISOString(),
            enqueued_by: 'mps_is_ready_so_publish_retry'
          });

        if (retryError) {
          console.error(`[taskQueueWorker.js] Error creating retry task for MPS ${payload?.id}:`, retryError);
          await logError(`Error creating retry task for MPS ${payload?.id}: ${retryError.message}`, `Processing task ${task.id}`);

          // If we can't create retry, mark as error
          await updateTaskStatus(task.id, 'error', {
            message: "Failed to publish MPS due to an unexpected error and failed to schedule retry.",
            error: err.message
          });
        } else {
          console.log(`[taskQueueWorker.js] Successfully scheduled retry for MPS ${payload?.id} at ${retryTime.toISOString()}`);

          // Mark original task as completed with retry info
          await updateTaskStatus(task.id, 'completed', {
            message: "First publish attempt failed due to exception. Retry scheduled for 5 minutes later.",
            mps_id: payload?.id,
            retry_scheduled_at: retryTime.toISOString(),
            original_error: err.message
          });
        }
      } catch (retryErr) {
        console.error(`[taskQueueWorker.js] Exception while scheduling retry for MPS ${payload?.id}:`, retryErr);
        await logError(`Exception while scheduling retry for MPS ${payload?.id}: ${retryErr.message}`, `Processing task ${task.id}`);

        // If we can't schedule retry, mark as error
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to publish MPS due to an unexpected error and failed to schedule retry due to exception.",
          error: err.message
        });
      }
    } else {
      // Second failure - mark as error permanently
      console.log(`[taskQueueWorker.js] Second exception for MPS ${payload?.id || 'unknown'}, marking as permanent error`);

      await updateTaskStatus(task.id, 'error', {
        message: "Failed to publish MPS due to an unexpected error after retry attempt.",
        error: err.message,
        retry_attempt: true
      });
    }
  }
}

// Function to process a d_sold_or_unsold_update_sdasin_inv task
async function processDiscSoldOrUnsoldUpdateSdasinInvTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        // Parse JSON string
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload format: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process disc sold/unsold SDASIN inventory update task. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing disc id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process disc sold/unsold SDASIN inventory update task. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;
    const oldSoldDate = payload.old_sold_date;
    const newSoldDate = payload.new_sold_date;

    console.log(`[taskQueueWorker.js] Processing disc sold/unsold SDASIN inventory update for disc id=${discId}`);
    console.log(`[taskQueueWorker.js] Old sold_date: ${oldSoldDate}, New sold_date: ${newSoldDate}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Determine if the disc was sold or unsold
    if (oldSoldDate === null && newSoldDate !== null) {
      // Disc was sold - decrease inventory
      console.log(`[taskQueueWorker.js] Disc ${discId} was sold - decreasing SDASIN inventory`);

      // Get the related SDASIN IDs
      const { data: sdasinIds, error: fetchError } = await supabase
        .from('tjoin_discs_sdasins')
        .select('sdasin_id')
        .eq('disc_id', discId);

      if (fetchError) {
        const errMsg = `[taskQueueWorker.js] Error fetching related SDASIN IDs: ${fetchError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Fetching related SDASIN IDs for disc id=${discId}`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to process disc sold/unsold SDASIN inventory update task. Error fetching related SDASIN IDs.",
          error: fetchError.message
        });
        return;
      }

      if (!sdasinIds || sdasinIds.length === 0) {
        console.log(`[taskQueueWorker.js] No related SDASIN IDs found for disc id=${discId}`);
        await updateTaskStatus(task.id, 'completed', {
          message: "No related SDASIN IDs found for this disc.",
          disc_id: discId
        });
        return;
      }

      console.log(`[taskQueueWorker.js] Found ${sdasinIds.length} related SDASIN IDs for disc id=${discId}`);

      // Update each related SDASIN inventory
      let successCount = 0;
      let errorCount = 0;
      let errors = [];

      for (const { sdasin_id } of sdasinIds) {
        try {
          console.log(`[taskQueueWorker.js] Decreasing inventory for SDASIN id=${sdasin_id}`);

          // First, get the current available_quantity
          const { data: sdasinData, error: fetchError } = await supabase
            .from('t_inv_sdasin')
            .select('available_quantity')
            .eq('id', sdasin_id)
            .single();

          if (fetchError) {
            const errMsg = `[taskQueueWorker.js] Error fetching current inventory for SDASIN id=${sdasin_id}: ${fetchError.message}`;
            console.error(errMsg);
            await logError(errMsg, `Fetching inventory for SDASIN id=${sdasin_id}`);
            errorCount++;
            errors.push({ sdasin_id, error: fetchError.message });
            continue;
          }

          if (!sdasinData) {
            const errMsg = `[taskQueueWorker.js] No SDASIN record found with id=${sdasin_id}`;
            console.error(errMsg);
            await logError(errMsg, `Fetching inventory for SDASIN id=${sdasin_id}`);
            errorCount++;
            errors.push({ sdasin_id, error: 'No SDASIN record found' });
            continue;
          }

          // Calculate the new available_quantity
          const currentQuantity = sdasinData.available_quantity || 0;
          const newQuantity = Math.max(0, currentQuantity - 1); // Ensure it doesn't go below 0

          // Update the record with the new quantity
          const { error: updateError } = await supabase
            .from('t_inv_sdasin')
            .update({
              available_quantity: newQuantity
            })
            .eq('id', sdasin_id);

          if (updateError) {
            const errMsg = `[taskQueueWorker.js] Error updating inventory for SDASIN id=${sdasin_id}: ${updateError.message}`;
            console.error(errMsg);
            await logError(errMsg, `Updating inventory for SDASIN id=${sdasin_id}`);
            errorCount++;
            errors.push({ sdasin_id, error: updateError.message });
          } else {
            console.log(`[taskQueueWorker.js] Successfully decreased inventory for SDASIN id=${sdasin_id}`);
            successCount++;
          }
        } catch (err) {
          const errMsg = `[taskQueueWorker.js] Exception updating inventory for SDASIN id=${sdasin_id}: ${err.message}`;
          console.error(errMsg);
          await logError(errMsg, `Updating inventory for SDASIN id=${sdasin_id}`);
          errorCount++;
          errors.push({ sdasin_id, error: err.message });
        }
      }

      // Update task status
      if (errorCount === 0) {
        await updateTaskStatus(task.id, 'completed', {
          message: `Successfully decreased inventory for ${successCount} SDASIN records.`,
          disc_id: discId,
          success_count: successCount
        });
      } else if (successCount === 0) {
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to update inventory for any SDASIN records.",
          disc_id: discId,
          error_count: errorCount,
          errors
        });
      } else {
        await updateTaskStatus(task.id, 'completed', {
          message: `Partially successful: Updated ${successCount} SDASIN records, failed to update ${errorCount} records.`,
          disc_id: discId,
          success_count: successCount,
          error_count: errorCount,
          errors
        });
      }
    } else if (oldSoldDate !== null && newSoldDate === null) {
      // Disc was unsold - increase inventory
      console.log(`[taskQueueWorker.js] Disc ${discId} was unsold - increasing SDASIN inventory`);

      // Get the related SDASIN IDs
      const { data: sdasinIds, error: fetchError } = await supabase
        .from('tjoin_discs_sdasins')
        .select('sdasin_id')
        .eq('disc_id', discId);

      if (fetchError) {
        const errMsg = `[taskQueueWorker.js] Error fetching related SDASIN IDs: ${fetchError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Fetching related SDASIN IDs for disc id=${discId}`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to process disc sold/unsold SDASIN inventory update task. Error fetching related SDASIN IDs.",
          error: fetchError.message
        });
        return;
      }

      if (!sdasinIds || sdasinIds.length === 0) {
        console.log(`[taskQueueWorker.js] No related SDASIN IDs found for disc id=${discId}`);
        await updateTaskStatus(task.id, 'completed', {
          message: "No related SDASIN IDs found for this disc.",
          disc_id: discId
        });
        return;
      }

      console.log(`[taskQueueWorker.js] Found ${sdasinIds.length} related SDASIN IDs for disc id=${discId}`);

      // Update each related SDASIN inventory
      let successCount = 0;
      let errorCount = 0;
      let errors = [];

      for (const { sdasin_id } of sdasinIds) {
        try {
          console.log(`[taskQueueWorker.js] Increasing inventory for SDASIN id=${sdasin_id}`);

          // First, get the current available_quantity
          const { data: sdasinData, error: fetchError } = await supabase
            .from('t_inv_sdasin')
            .select('available_quantity')
            .eq('id', sdasin_id)
            .single();

          if (fetchError) {
            const errMsg = `[taskQueueWorker.js] Error fetching current inventory for SDASIN id=${sdasin_id}: ${fetchError.message}`;
            console.error(errMsg);
            await logError(errMsg, `Fetching inventory for SDASIN id=${sdasin_id}`);
            errorCount++;
            errors.push({ sdasin_id, error: fetchError.message });
            continue;
          }

          if (!sdasinData) {
            const errMsg = `[taskQueueWorker.js] No SDASIN record found with id=${sdasin_id}`;
            console.error(errMsg);
            await logError(errMsg, `Fetching inventory for SDASIN id=${sdasin_id}`);
            errorCount++;
            errors.push({ sdasin_id, error: 'No SDASIN record found' });
            continue;
          }

          // Calculate the new available_quantity
          const currentQuantity = sdasinData.available_quantity || 0;
          const newQuantity = currentQuantity + 1;

          // Update the record with the new quantity
          const { error: updateError } = await supabase
            .from('t_inv_sdasin')
            .update({
              available_quantity: newQuantity
            })
            .eq('id', sdasin_id);

          if (updateError) {
            const errMsg = `[taskQueueWorker.js] Error updating inventory for SDASIN id=${sdasin_id}: ${updateError.message}`;
            console.error(errMsg);
            await logError(errMsg, `Updating inventory for SDASIN id=${sdasin_id}`);
            errorCount++;
            errors.push({ sdasin_id, error: updateError.message });
          } else {
            console.log(`[taskQueueWorker.js] Successfully increased inventory for SDASIN id=${sdasin_id}`);
            successCount++;
          }
        } catch (err) {
          const errMsg = `[taskQueueWorker.js] Exception updating inventory for SDASIN id=${sdasin_id}: ${err.message}`;
          console.error(errMsg);
          await logError(errMsg, `Updating inventory for SDASIN id=${sdasin_id}`);
          errorCount++;
          errors.push({ sdasin_id, error: err.message });
        }
      }

      // Update task status
      if (errorCount === 0) {
        await updateTaskStatus(task.id, 'completed', {
          message: `Successfully increased inventory for ${successCount} SDASIN records.`,
          disc_id: discId,
          success_count: successCount
        });
      } else if (successCount === 0) {
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to update inventory for any SDASIN records.",
          disc_id: discId,
          error_count: errorCount,
          errors
        });
      } else {
        await updateTaskStatus(task.id, 'completed', {
          message: `Partially successful: Updated ${successCount} SDASIN records, failed to update ${errorCount} records.`,
          disc_id: discId,
          success_count: successCount,
          error_count: errorCount,
          errors
        });
      }
    } else {
      // This shouldn't happen based on the trigger condition, but handle it just in case
      const errMsg = `[taskQueueWorker.js] Unexpected sold_date change for disc id=${discId}: old=${oldSoldDate}, new=${newSoldDate}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process disc sold/unsold SDASIN inventory update task. Unexpected sold_date change.",
        error: 'Unexpected sold_date change',
        disc_id: discId,
        old_sold_date: oldSoldDate,
        new_sold_date: newSoldDate
      });
    }
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process disc sold/unsold SDASIN inventory update task due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a disc_sold_or_unsold task
async function processDiscSoldOrUnsoldTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        // Parse JSON string
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload format: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process disc sold/unsold task. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing disc id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process disc sold/unsold task. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;
    const oldSoldDate = payload.old_sold_date;
    const newSoldDate = payload.new_sold_date;
    const shopifyUploadedAt = payload.shopify_uploaded_at;

    console.log(`[taskQueueWorker.js] Processing disc sold/unsold for disc id=${discId}`);
    console.log(`[taskQueueWorker.js] Old sold_date: ${oldSoldDate}, New sold_date: ${newSoldDate}`);
    console.log(`[taskQueueWorker.js] Shopify uploaded at: ${shopifyUploadedAt}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Enqueue tasks based on the sold/unsold status
    const tasks = [];
    let taskErrors = [];

    // 1. Always enqueue SDASIN inventory update task
    try {
      console.log(`[taskQueueWorker.js] Enqueueing d_sold_or_unsold_update_sdasin_inv task for disc id=${discId}`);

      const { data: sdasinTask, error: sdasinError } = await supabase
        .from('t_task_queue')
        .insert({
          task_type: 'd_sold_or_unsold_update_sdasin_inv',
          payload: {
            id: discId,
            old_sold_date: oldSoldDate,
            new_sold_date: newSoldDate
          },
          status: 'pending',
          scheduled_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: task.task_type
        })
        .select();

      if (sdasinError) {
        const errMsg = `[taskQueueWorker.js] Error enqueueing d_sold_or_unsold_update_sdasin_inv task: ${sdasinError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Enqueueing task for disc id=${discId}`);
        taskErrors.push({
          task_type: 'd_sold_or_unsold_update_sdasin_inv',
          error: sdasinError.message
        });
      } else {
        tasks.push({
          task_type: 'd_sold_or_unsold_update_sdasin_inv',
          task_id: sdasinTask[0].id
        });
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Exception enqueueing d_sold_or_unsold_update_sdasin_inv task: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Enqueueing task for disc id=${discId}`);
      taskErrors.push({
        task_type: 'd_sold_or_unsold_update_sdasin_inv',
        error: err.message
      });
    }

    // 2. Enqueue Veeqo quantity update task if the disc has been uploaded to Shopify
    if (shopifyUploadedAt) {
      try {
        // Determine the task type based on whether the disc was sold or unsold
        const veeqoTaskType = oldSoldDate === null && newSoldDate !== null
          ? 'disc_sold_update_veeqo_qty_by_d_sku'    // Disc was sold
          : 'disc_unsold_update_veeqo_qty_by_d_sku'; // Disc was unsold

        console.log(`[taskQueueWorker.js] Enqueueing ${veeqoTaskType} task for disc id=${discId}`);

        const { data: veeqoTask, error: veeqoError } = await supabase
          .from('t_task_queue')
          .insert({
            task_type: veeqoTaskType,
            payload: { id: discId },
            status: 'pending',
            scheduled_at: new Date().toISOString(),
            created_at: new Date().toISOString(),
            enqueued_by: task.task_type
          })
          .select();

        if (veeqoError) {
          const errMsg = `[taskQueueWorker.js] Error enqueueing ${veeqoTaskType} task: ${veeqoError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Enqueueing task for disc id=${discId}`);
          taskErrors.push({
            task_type: veeqoTaskType,
            error: veeqoError.message
          });
        } else {
          tasks.push({
            task_type: veeqoTaskType,
            task_id: veeqoTask[0].id
          });
        }
      } catch (err) {
        const errMsg = `[taskQueueWorker.js] Exception enqueueing Veeqo task: ${err.message}`;
        console.error(errMsg);
        await logError(errMsg, `Enqueueing Veeqo task for disc id=${discId}`);
        taskErrors.push({
          task_type: 'veeqo_update',
          error: err.message
        });
      }

      // 3. Enqueue Shopify direct update task with a 5-minute delay
      try {
        console.log(`[taskQueueWorker.js] Enqueueing d_sold_or_unsold_udpate_shopify_directly task for disc id=${discId}`);

        // Calculate scheduled time (5 minutes from now)
        const scheduledAt = new Date();
        scheduledAt.setMinutes(scheduledAt.getMinutes() + 5);

        const { data: shopifyTask, error: shopifyError } = await supabase
          .from('t_task_queue')
          .insert({
            task_type: 'd_sold_or_unsold_udpate_shopify_directly',
            payload: {
              id: discId,
              old_sold_date: oldSoldDate,
              new_sold_date: newSoldDate
            },
            status: 'pending',
            scheduled_at: scheduledAt.toISOString(),
            created_at: new Date().toISOString(),
            enqueued_by: task.task_type
          })
          .select();

        if (shopifyError) {
          const errMsg = `[taskQueueWorker.js] Error enqueueing d_sold_or_unsold_udpate_shopify_directly task: ${shopifyError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Enqueueing task for disc id=${discId}`);
          taskErrors.push({
            task_type: 'd_sold_or_unsold_udpate_shopify_directly',
            error: shopifyError.message
          });
        } else {
          tasks.push({
            task_type: 'd_sold_or_unsold_udpate_shopify_directly',
            task_id: shopifyTask[0].id
          });
        }
      } catch (err) {
        const errMsg = `[taskQueueWorker.js] Exception enqueueing Shopify direct update task: ${err.message}`;
        console.error(errMsg);
        await logError(errMsg, `Enqueueing Shopify task for disc id=${discId}`);
        taskErrors.push({
          task_type: 'd_sold_or_unsold_udpate_shopify_directly',
          error: err.message
        });
      }

      // 4. Enqueue OSL inventory update task
      try {
        console.log(`[taskQueueWorker.js] Enqueueing d_sold_or_unsold_update_osl_inv task for disc id=${discId}`);

        const { data: oslTask, error: oslError } = await supabase
          .from('t_task_queue')
          .insert({
            task_type: 'd_sold_or_unsold_update_osl_inv',
            payload: {
              id: discId,
              old_sold_date: oldSoldDate,
              new_sold_date: newSoldDate
            },
            status: 'pending',
            scheduled_at: new Date().toISOString(),
            created_at: new Date().toISOString(),
            enqueued_by: task.task_type
          })
          .select();

        if (oslError) {
          const errMsg = `[taskQueueWorker.js] Error enqueueing d_sold_or_unsold_update_osl_inv task: ${oslError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Enqueueing task for disc id=${discId}`);
          taskErrors.push({
            task_type: 'd_sold_or_unsold_update_osl_inv',
            error: oslError.message
          });
        } else {
          tasks.push({
            task_type: 'd_sold_or_unsold_update_osl_inv',
            task_id: oslTask[0].id
          });
        }
      } catch (err) {
        const errMsg = `[taskQueueWorker.js] Exception enqueueing OSL inventory update task: ${err.message}`;
        console.error(errMsg);
        await logError(errMsg, `Enqueueing OSL task for disc id=${discId}`);
        taskErrors.push({
          task_type: 'd_sold_or_unsold_update_osl_inv',
          error: err.message
        });
      }
    }

    // Update task status based on results
    if (taskErrors.length === 0) {
      await updateTaskStatus(task.id, 'completed', {
        message: `Successfully enqueued ${tasks.length} tasks for disc id=${discId}`,
        disc_id: discId,
        tasks: tasks
      });
    } else if (tasks.length === 0) {
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to enqueue any tasks.",
        disc_id: discId,
        errors: taskErrors
      });
    } else {
      await updateTaskStatus(task.id, 'completed', {
        message: `Partially successful: Enqueued ${tasks.length} tasks, failed to enqueue ${taskErrors.length} tasks`,
        disc_id: discId,
        tasks: tasks,
        errors: taskErrors
      });
    }
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process disc sold/unsold task due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a d_sold_or_unsold_udpate_shopify_directly task
async function processDiscSoldOrUnsoldUpdateShopifyDirectlyTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        // Parse JSON string
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload format: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process disc sold/unsold Shopify update task. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing disc id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process disc sold/unsold Shopify update task. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;
    const oldSoldDate = payload.old_sold_date;
    const newSoldDate = payload.new_sold_date;

    console.log(`[taskQueueWorker.js] Processing disc sold/unsold Shopify update for disc id=${discId}`);
    console.log(`[taskQueueWorker.js] Old sold_date: ${oldSoldDate}, New sold_date: ${newSoldDate}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // First, check if the disc has been uploaded to Shopify
    const { data: discData, error: discError } = await supabase
      .from('t_discs')
      .select('shopify_uploaded_at')
      .eq('id', discId)
      .single();

    if (discError) {
      const errMsg = `[taskQueueWorker.js] Error fetching disc data: ${discError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching disc data for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process disc sold/unsold Shopify update task. Error fetching disc data.",
        error: discError.message
      });
      return;
    }

    if (!discData || !discData.shopify_uploaded_at) {
      console.log(`[taskQueueWorker.js] Disc id=${discId} has not been uploaded to Shopify, skipping Shopify update`);
      await updateTaskStatus(task.id, 'completed', {
        message: "Disc has not been uploaded to Shopify, no Shopify update needed.",
        disc_id: discId
      });
      return;
    }

    // Get the variant information from the database
    console.log(`[taskQueueWorker.js] Fetching variant information from database for disc id=${discId}`);

    // Get the variant information from the database
    const { data, error: variantError } = await supabase
      .from('t_discs')
      .select('shopify_uploaded_at')
      .eq('id', discId)
      .single();

    // Initialize variantData as an empty object
    let variantData = {};

    if (variantError) {
      const errMsg = `[taskQueueWorker.js] Error fetching variant information for disc id=${discId}: ${variantError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching variant information for disc id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process disc sold/unsold Shopify update task. Error fetching variant information.",
        error: variantError.message
      });
      return;
    }

    if (!variantData || !variantData.shopify_variant_inventory_item_id) {
      // If we don't have the variant information in t_discs, try to get it from the reconciliation view
      console.log(`[taskQueueWorker.js] No variant information found in t_discs for disc id=${discId}, checking reconciliation view`);

      // Try to find the variant by SKU
      const dSku = `D${discId}`;
      console.log(`[taskQueueWorker.js] Looking up variant by SKU: ${dSku}`);

      try {
        // Import the Shopify GraphQL utility
        const { findVariantBySku } = await import('./shopifyGraphQL.js');

        // Find the variant by SKU
        const variantInfo = await findVariantBySku(dSku);

        if (variantInfo && variantInfo.inventoryItemId) {
          console.log(`[taskQueueWorker.js] Found variant by SKU ${dSku}: ${JSON.stringify(variantInfo)}`);

          // Use the variant information from the GraphQL API
          variantData.shopify_variant_inventory_item_id = variantInfo.inventoryItemId;
          variantData.shopify_variant_id = variantInfo.variantId;
          variantData.shopify_product_id = variantInfo.productId;

          console.log(`[taskQueueWorker.js] Using variant information from GraphQL API for disc id=${discId}`);
        } else {
          console.log(`[taskQueueWorker.js] No variant found by SKU ${dSku}, checking reconciliation view`);

          // Fall back to the reconciliation view
          const { data: reconcileData, error: reconcileError } = await supabase
            .from('v_reconcile_d_to_shopify')
            .select('shopify_product_id, shopify_variant_id, shopify_variant_inventory_item_id')
            .eq('disc_id', discId);

          if (reconcileError) {
            const errMsg = `[taskQueueWorker.js] Error fetching variant information from reconciliation view for disc id=${discId}: ${reconcileError.message}`;
            console.error(errMsg);
            await logError(errMsg, `Fetching variant information from reconciliation view for disc id=${discId}`);
            await updateTaskStatus(task.id, 'error', {
              message: "Failed to process disc sold/unsold Shopify update task. Error fetching variant information from reconciliation view.",
              error: reconcileError.message
            });
            return;
          }

          if (!reconcileData || reconcileData.length === 0 || !reconcileData[0].shopify_variant_inventory_item_id) {
            const errMsg = `[taskQueueWorker.js] No Shopify variant information found for disc id=${discId}`;
            console.error(errMsg);
            await logError(errMsg, `Fetching variant information for disc id=${discId}`);
            await updateTaskStatus(task.id, 'completed', {
              message: "No Shopify variant information found. The disc may not be properly linked to Shopify.",
              disc_id: discId
            });
            return;
          }

          // Create a new variantData object from the reconciliation view data
          variantData = {
            shopify_product_id: reconcileData[0].shopify_product_id,
            shopify_variant_id: reconcileData[0].shopify_variant_id,
            shopify_variant_inventory_item_id: reconcileData[0].shopify_variant_inventory_item_id
          };
          console.log(`[taskQueueWorker.js] Found variant information in reconciliation view for disc id=${discId}`);
        }
      } catch (err) {
        const errMsg = `[taskQueueWorker.js] Error looking up variant by SKU: ${err.message}`;
        console.error(errMsg);
        await logError(errMsg, `Looking up variant by SKU for disc id=${discId}`);

        // Fall back to the reconciliation view
        const { data: reconcileData, error: reconcileError } = await supabase
          .from('v_reconcile_d_to_shopify')
          .select('shopify_product_id, shopify_variant_id, shopify_variant_inventory_item_id')
          .eq('disc_id', discId);

        if (reconcileError) {
          const errMsg = `[taskQueueWorker.js] Error fetching variant information from reconciliation view for disc id=${discId}: ${reconcileError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Fetching variant information from reconciliation view for disc id=${discId}`);
          await updateTaskStatus(task.id, 'error', {
            message: "Failed to process disc sold/unsold Shopify update task. Error fetching variant information from reconciliation view.",
            error: reconcileError.message
          });
          return;
        }

        if (!reconcileData || reconcileData.length === 0 || !reconcileData[0].shopify_variant_inventory_item_id) {
          const errMsg = `[taskQueueWorker.js] No Shopify variant information found for disc id=${discId}`;
          console.error(errMsg);
          await logError(errMsg, `Fetching variant information for disc id=${discId}`);
          await updateTaskStatus(task.id, 'completed', {
            message: "No Shopify variant information found. The disc may not be properly linked to Shopify.",
            disc_id: discId
          });
          return;
        }

        // Create a new variantData object from the reconciliation view data
        variantData = {
          shopify_product_id: reconcileData[0].shopify_product_id,
          shopify_variant_id: reconcileData[0].shopify_variant_id,
          shopify_variant_inventory_item_id: reconcileData[0].shopify_variant_inventory_item_id
        };
        console.log(`[taskQueueWorker.js] Found variant information in reconciliation view for disc id=${discId}`);
      }
    }

    // Determine the target quantity based on the sold_date
    const targetQuantity = newSoldDate === null ? 1 : 0;
    console.log(`[taskQueueWorker.js] Setting Shopify inventory to ${targetQuantity} for disc id=${discId} with shopify_variant_inventory_item_id=${variantData.shopify_variant_inventory_item_id}`);

    try {
      // Import the Shopify GraphQL utility
      const { setInventoryItemQuantity } = await import('./shopifyGraphQL.js');

      // Set the inventory to the target quantity
      const result = await setInventoryItemQuantity(variantData.shopify_variant_inventory_item_id, targetQuantity);

      console.log(`[taskQueueWorker.js] Successfully set Shopify inventory to ${targetQuantity} for disc id=${discId} with shopify_variant_inventory_item_id=${variantData.shopify_variant_inventory_item_id}`);
      console.log(`[taskQueueWorker.js] Result: ${JSON.stringify(result)}`);

      // Update task status to completed
      await updateTaskStatus(task.id, 'completed', {
        message: `Successfully set Shopify inventory to ${targetQuantity} for disc id=${discId}`,
        disc_id: discId,
        shopify_product_id: variantData.shopify_product_id,
        shopify_variant_id: variantData.shopify_variant_id,
        shopify_variant_inventory_item_id: variantData.shopify_variant_inventory_item_id,
        target_quantity: targetQuantity,
        result
      });
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error setting inventory to ${targetQuantity} for disc id=${discId} with shopify_variant_inventory_item_id=${variantData.shopify_variant_inventory_item_id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Setting inventory for disc id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process disc sold/unsold Shopify update task. Error setting inventory.",
        error: err.message,
        disc_id: discId,
        shopify_product_id: variantData.shopify_product_id,
        shopify_variant_id: variantData.shopify_variant_id,
        shopify_variant_inventory_item_id: variantData.shopify_variant_inventory_item_id,
        target_quantity: targetQuantity
      });
    }
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process disc sold/unsold Shopify update task due to an unexpected error.",
      error: err.message
    });
  }
}

// Function to process a d_sold_or_unsold_update_osl_inv task
async function processDiscSoldOrUnsoldUpdateOslInvTask(task) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        // Parse JSON string
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload format: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process disc sold/unsold OSL inventory update task. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing disc id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process disc sold/unsold OSL inventory update task. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;
    const oldSoldDate = payload.old_sold_date;
    const newSoldDate = payload.new_sold_date;

    console.log(`[taskQueueWorker.js] Processing disc sold/unsold OSL inventory update for disc id=${discId}`);
    console.log(`[taskQueueWorker.js] Old sold_date: ${oldSoldDate}, New sold_date: ${newSoldDate}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // First, check if the disc has an order_sheet_line_id
    const { data: discData, error: discError } = await supabase
      .from('t_discs')
      .select('order_sheet_line_id')
      .eq('id', discId)
      .single();

    if (discError) {
      const errMsg = `[taskQueueWorker.js] Error fetching disc data: ${discError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching disc data for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process disc sold/unsold OSL inventory update task. Error fetching disc data.",
        error: discError.message
      });
      return;
    }

    if (!discData || !discData.order_sheet_line_id) {
      console.log(`[taskQueueWorker.js] Disc id=${discId} has no order_sheet_line_id, skipping OSL inventory update`);
      await updateTaskStatus(task.id, 'completed', {
        message: "Disc has no order_sheet_line_id, no OSL inventory update needed.",
        disc_id: discId
      });
      return;
    }

    const oslId = discData.order_sheet_line_id;
    console.log(`[taskQueueWorker.js] Disc id=${discId} has order_sheet_line_id=${oslId}`);

    // Determine if the disc was sold or unsold
    if (oldSoldDate === null && newSoldDate !== null) {
      // Disc was sold - decrease inventory
      console.log(`[taskQueueWorker.js] Disc ${discId} was sold - decreasing OSL inventory for OSL id=${oslId}`);

      // Get the current available_quantity
      const { data: oslData, error: fetchError } = await supabase
        .from('t_inv_osl')
        .select('available_quantity')
        .eq('id', oslId)
        .single();

      if (fetchError) {
        const errMsg = `[taskQueueWorker.js] Error fetching current inventory for OSL id=${oslId}: ${fetchError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Fetching inventory for OSL id=${oslId}`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to process disc sold/unsold OSL inventory update task. Error fetching OSL inventory.",
          error: fetchError.message
        });
        return;
      }

      if (!oslData) {
        // Create a new record if it doesn't exist
        console.log(`[taskQueueWorker.js] No t_inv_osl record found for id=${oslId}, creating new record with available_quantity=0`);

        const { error: insertError } = await supabase
          .from('t_inv_osl')
          .insert({
            id: oslId,
            available_quantity: 0
          });

        if (insertError) {
          const errMsg = `[taskQueueWorker.js] Error creating t_inv_osl record for id=${oslId}: ${insertError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Creating t_inv_osl record for id=${oslId}`);
          await updateTaskStatus(task.id, 'error', {
            message: "Failed to process disc sold/unsold OSL inventory update task. Error creating OSL inventory record.",
            error: insertError.message
          });
          return;
        }

        console.log(`[taskQueueWorker.js] Successfully created t_inv_osl record for id=${oslId} with available_quantity=0`);
        await updateTaskStatus(task.id, 'completed', {
          message: "Created new OSL inventory record with available_quantity=0.",
          disc_id: discId,
          osl_id: oslId
        });
        return;
      }

      // Calculate the new available_quantity
      const currentQuantity = oslData.available_quantity || 0;
      const newQuantity = Math.max(0, currentQuantity - 1); // Ensure it doesn't go below 0

      // Update the record with the new quantity
      const { error: updateError } = await supabase
        .from('t_inv_osl')
        .update({
          available_quantity: newQuantity
        })
        .eq('id', oslId);

      if (updateError) {
        const errMsg = `[taskQueueWorker.js] Error updating inventory for OSL id=${oslId}: ${updateError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Updating inventory for OSL id=${oslId}`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to process disc sold/unsold OSL inventory update task. Error updating OSL inventory.",
          error: updateError.message
        });
        return;
      }

      console.log(`[taskQueueWorker.js] Successfully decreased inventory for OSL id=${oslId} from ${currentQuantity} to ${newQuantity}`);
      await updateTaskStatus(task.id, 'completed', {
        message: `Successfully decreased OSL inventory from ${currentQuantity} to ${newQuantity}.`,
        disc_id: discId,
        osl_id: oslId,
        old_quantity: currentQuantity,
        new_quantity: newQuantity
      });
    } else if (oldSoldDate !== null && newSoldDate === null) {
      // Disc was unsold - increase inventory
      console.log(`[taskQueueWorker.js] Disc ${discId} was unsold - increasing OSL inventory for OSL id=${oslId}`);

      // Get the current available_quantity
      const { data: oslData, error: fetchError } = await supabase
        .from('t_inv_osl')
        .select('available_quantity')
        .eq('id', oslId)
        .single();

      if (fetchError) {
        const errMsg = `[taskQueueWorker.js] Error fetching current inventory for OSL id=${oslId}: ${fetchError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Fetching inventory for OSL id=${oslId}`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to process disc sold/unsold OSL inventory update task. Error fetching OSL inventory.",
          error: fetchError.message
        });
        return;
      }

      if (!oslData) {
        // Create a new record if it doesn't exist
        console.log(`[taskQueueWorker.js] No t_inv_osl record found for id=${oslId}, creating new record with available_quantity=1`);

        const { error: insertError } = await supabase
          .from('t_inv_osl')
          .insert({
            id: oslId,
            available_quantity: 1
          });

        if (insertError) {
          const errMsg = `[taskQueueWorker.js] Error creating t_inv_osl record for id=${oslId}: ${insertError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Creating t_inv_osl record for id=${oslId}`);
          await updateTaskStatus(task.id, 'error', {
            message: "Failed to process disc sold/unsold OSL inventory update task. Error creating OSL inventory record.",
            error: insertError.message
          });
          return;
        }

        console.log(`[taskQueueWorker.js] Successfully created t_inv_osl record for id=${oslId} with available_quantity=1`);
        await updateTaskStatus(task.id, 'completed', {
          message: "Created new OSL inventory record with available_quantity=1.",
          disc_id: discId,
          osl_id: oslId
        });
        return;
      }

      // Calculate the new available_quantity
      const currentQuantity = oslData.available_quantity || 0;
      const newQuantity = currentQuantity + 1;

      // Update the record with the new quantity
      const { error: updateError } = await supabase
        .from('t_inv_osl')
        .update({
          available_quantity: newQuantity
        })
        .eq('id', oslId);

      if (updateError) {
        const errMsg = `[taskQueueWorker.js] Error updating inventory for OSL id=${oslId}: ${updateError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Updating inventory for OSL id=${oslId}`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to process disc sold/unsold OSL inventory update task. Error updating OSL inventory.",
          error: updateError.message
        });
        return;
      }

      console.log(`[taskQueueWorker.js] Successfully increased inventory for OSL id=${oslId} from ${currentQuantity} to ${newQuantity}`);
      await updateTaskStatus(task.id, 'completed', {
        message: `Successfully increased OSL inventory from ${currentQuantity} to ${newQuantity}.`,
        disc_id: discId,
        osl_id: oslId,
        old_quantity: currentQuantity,
        new_quantity: newQuantity
      });
    } else {
      // This shouldn't happen based on the trigger condition, but handle it just in case
      const errMsg = `[taskQueueWorker.js] Unexpected sold_date change for disc id=${discId}: old=${oldSoldDate}, new=${newSoldDate}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process disc sold/unsold OSL inventory update task. Unexpected sold_date change.",
        error: 'Unexpected sold_date change',
        disc_id: discId,
        old_sold_date: oldSoldDate,
        new_sold_date: newSoldDate
      });
    }
  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process disc sold/unsold OSL inventory update task due to an unexpected error.",
      error: err.message
    });
  }
}

// Export the processTaskQueue function for direct import
export async function processTaskQueue() {
  return main();
}

// If this script is run directly (not imported), run the worker
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(err => {
    console.error(`[taskQueueWorker.js] Unhandled error: ${err.message}`);
    console.error(err.stack);
    process.exit(1);
  });
}

